id: cache-redis-multiaz-disabled

info:
  name: ElastiCache Redis Multi-AZ - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that your Amazon ElastiCache Redis cache clusters are using a Multi-AZ deployment configuration to enhance reliability through automatic failover.
  impact: |
    Disabling ElastiCache Redis Multi-AZ can lead to data loss and increased downtime in the event of a primary node failure, as failover to a secondary node in a different availability zone will not occur automatically.
  remediation: |
    Enable Multi-AZ replication in the ElastiCache Redis settings or create a new cluster with Multi-AZ enabled to ensure high availability.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/ElastiCache/elasticache-multi-az.html
    - https://docs.aws.amazon.com/AmazonElastiCache/latest/red-ug/WhatIs.Components.html
  tags: cloud,devops,aws,amazon,elasticache,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let ReplicationGroupId of iterate(template.replicationgroupids)){
    set("replicationgroup", ReplicationGroupId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws elasticache describe-replication-groups --region $region --output json --query 'ReplicationGroups[*].ReplicationGroupId'

    extractors:
      - type: json
        name: replicationgroupids
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws elasticache describe-replication-groups --region $region --replication-group-id $replicationgroup --query 'ReplicationGroups[*].MultiAZ'

    matchers:
      - type: word
        words:
          - 'disabled'

    extractors:
      - type: dsl
        dsl:
          - 'replicationgroup + " ElastiCache Redis Multi-AZ is Disabled"'
# digest: 490a004630440220427ed84b4f9fec905f6280bd6ce8366c3d91d8edd28a9ac79d32974a916da726022005c02d872966a4591fd1424386aaaa0f985e92a88dc9908525b86187d8769488:922c64590222798bb761d5b6d8e72950