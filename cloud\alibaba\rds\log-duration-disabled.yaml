id: log-duration-disabled

info:
  name: PostgreSQL "log_duration" Parameter - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    The log_duration parameter in PostgreSQL is disabled, meaning the database does not log the duration of SQL queries. This can make it difficult to monitor and analyze query performance or identify slow-running queries for optimization.
  reference:
    - https://www.alibabacloud.com/help/en/rds/apsaradb-rds-for-postgresql/use-a-parameter-template-to-configure-the-parameters-of-apsaradb-rds-for-postgresql-instances
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RDS/enable-log-duration-for-postgresql.html
  metadata:
    max-request: 2
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-rds

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for(let DBInstanceId of iterate(template.dbinstanceid)){
    set("instance", DBInstanceId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun rds DescribeDBInstances --region $region

    extractors:
      - type: json
        name: dbinstanceid
        internal: true
        json:
          - '.Items.DBInstance[].DBInstanceId'

  - engine:
      - sh
      - bash

    source: |
      aliyun rds DescribeParameters --DBInstanceId $dbinstanceid --region $region | jq -r '.RunningParameters.DBInstanceParameter[] | select(.ParameterName == "log_duration") | {ParameterName, ParameterValue}'

    matchers:
      - type: word
        words:
          - '"ParameterValue": "off"'
          - '"ParameterName": "log_duration"'
        condition: and

    extractors:
      - type: dsl
        dsl:
          - 'instance + " RDS log_duration Parameter for PostgreSQL Database Instances "'
# digest: 4a0a00473045022100b06ccbd5514c7efc8fe339304a4b8eca3059658f4fd88bec14f522d88b368bed022041a7e867fd144c3a2d560fb2699b1792f4b46cb494a94e3f4b5f4181daf5b812:922c64590222798bb761d5b6d8e72950