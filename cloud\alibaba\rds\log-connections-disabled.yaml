id: log-connections-disabled

info:
  name: PostgreSQL "log_connections" Parameter - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    The log_connections parameter in PostgreSQL is disabled, meaning the database does not log new client connections. This may limit the ability to monitor and track when clients connect to the database for security and auditing purposes.
  reference:
    - https://www.alibabacloud.com/help/en/rds/apsaradb-rds-for-postgresql/use-a-parameter-template-to-configure-the-parameters-of-apsaradb-rds-for-postgresql-instances
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RDS/enable-log-connections-for-postgresql.html
  metadata:
    max-request: 2
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-rds

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for(let DBInstanceId of iterate(template.dbinstanceid)){
    set("instance", DBInstanceId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun rds DescribeDBInstances --region $region

    extractors:
      - type: json
        name: dbinstanceid
        internal: true
        json:
          - '.Items.DBInstance[].DBInstanceId'

  - engine:
      - sh
      - bash

    source: |
      aliyun rds DescribeParameters --DBInstanceId $dbinstanceid --region $region | jq -r '.RunningParameters.DBInstanceParameter[] | select(.ParameterName == "log_connections") | {ParameterName, ParameterValue}'

    matchers:
      - type: word
        words:
          - '"ParameterValue": "off"'
          - '"ParameterName": "log_connections"'
        condition: and

    extractors:
      - type: dsl
        dsl:
          - 'instance + " RDS log_connections Parameter for PostgreSQL Database Instances "'
# digest: 4a0a00473045022078a4e044bf6cf5e47d63412049b74edf9c64b7f42127e7475656e08cc5e206ef022100a1e5174f0f1daf3fc9e6a8a3c2b496601a3832b61f19bdf35f71aa0922bcd22f:922c64590222798bb761d5b6d8e72950