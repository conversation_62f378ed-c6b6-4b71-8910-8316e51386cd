id: iam-db-auth
info:
  name: IAM Database Authentication
  author: princechaddha
  severity: medium
  description: |
    Ensure IAM Database Authentication is enabled for RDS instances, allowing IAM service to manage database access, thereby removing the need to store user credentials within database configurations.
  impact: |
    Without IAM Database Authentication, database credentials need to be managed internally, increasing the risk of credential leakage and unauthorized access.
  remediation: |
    Enable IAM Database Authentication for MySQL and PostgreSQL RDS database instances to leverage IAM for secure, token-based access control.
  reference:
    - https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/UsingWithRDS.IAMDBAuth.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config
variables:
  region: "ap-northeast-1"

flow: |
  code(1)
  for(let DBInstances of iterate(template.instances)){
    set("db", DBInstances)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --output json --query 'DBInstances[?Engine==`mysql` || Engine==`postgres`].DBInstanceIdentifier | []'

    extractors:
      - type: json
        name: instances
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws rds describe-db-instances --region $region --db-instance-identifier $db --query 'DBInstances[*].IAMDatabaseAuthenticationEnabled'

    matchers:
      - type: word
        words:
          - 'false'

    extractors:
      - type: dsl
        dsl:
          - '"Database Authentication feature is not enabled for RDS database instance " + db'
# digest: 490a00463044022063a51b81cf2a6f80f3a19a3ba2fea4cfcf0b107e6ed3e318130da0eb34af064b02203e3ed181443111e2a118232b3346491f33c28f61d82cd86f3751558da9570d50:922c64590222798bb761d5b6d8e72950