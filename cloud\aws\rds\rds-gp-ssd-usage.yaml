id: rds-gp-ssd-usage
info:
  name: RDS General Purpose SSD Usage
  author: princechaddha
  severity: high
  description: |
    Ensure Amazon RDS instances use General Purpose SSDs for cost-effective storage suitable for a wide range of workloads, except for applications needing over 10000 IOPS or 160 MiB/s throughput.
  impact: |
    Using Provisioned IOPS SSDs when not required can significantly increase AWS costs without providing necessary performance benefits.
  remediation: |
    Convert RDS instances from Provisioned IOPS to General Purpose SSDs to optimize costs without sacrificing I/O performance for most database workloads.
  reference:
    - https://aws.amazon.com/rds/features/storage/
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config
variables:
  region: "ap-northeast-1"

flow: |
  code(1)
  for(let DBInstances of iterate(template.instances)){
    set("db", DBInstances)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --output json --query 'DBInstances[*].DBInstanceIdentifier'

    extractors:
      - type: json
        name: instances
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws rds describe-db-instances --region $region --db-instance-identifier $db --query 'DBInstances[*].StorageType'

    matchers:
      - type: word
        words:
          - 'io1'

    extractors:
      - type: dsl
        dsl:
          - 'db + " RDS instance uses Provisioned IOPS SSD, not the most cost-effective storage"'
# digest: 490a0046304402206daa7e0be96e02cde1a9c838e4988df6965d0d6e4cdcd6b920a30793a97f568702207f9dfd3e4cb0011b1fa9001bc46f38956ca597d3a4a1f1594a0873d4bcb95e04:922c64590222798bb761d5b6d8e72950