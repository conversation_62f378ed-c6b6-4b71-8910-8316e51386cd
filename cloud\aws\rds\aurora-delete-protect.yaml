id: aurora-delete-protect
info:
  name: Aurora Cluster Deletion Protection
  author: princechaddha
  severity: medium
  description: |
    Ensure Amazon Aurora clusters have Deletion Protection enabled to prevent accidental data loss.
  impact: |
    Without Deletion Protection, Aurora clusters can be accidentally deleted, leading to irreversible data loss.
  remediation: |
    Enable Deletion Protection by modifying the Aurora cluster settings in the AWS Management Console or via the AWS CLI.
  reference:
    - https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/DBInstanceDeletionProtection.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,aurora,rds,aws-cloud-config
variables:
  region: "ap-northeast-1"

flow: |
  code(1)
  for(let clustername of iterate(template.clusters)){
    set("cluster", clustername)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-clusters --region $region --output json --query 'DBClusters[?Engine==`aurora-mysql` || Engine==`aurora-postgresql`].DBClusterIdentifier | []'

    extractors:
      - type: json
        name: clusters
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws rds describe-db-clusters --region $region --db-cluster-identifier $cluster--query 'DBClusters[*].DeletionProtection'

    matchers:
      - type: word
        words:
          - 'false'

    extractors:
      - type: dsl
        dsl:
          - '"Deletion Protection safety feature is not enabled for " + cluster'
# digest: 4a0a00473045022100e3859aaaa478bed03d4d145c87c574732729a84d8b3ed7d08567e299719fc2120220507c3e70335448d850e74572c307f74cf8df2519ee6dbef76dee59035be39669:922c64590222798bb761d5b6d8e72950