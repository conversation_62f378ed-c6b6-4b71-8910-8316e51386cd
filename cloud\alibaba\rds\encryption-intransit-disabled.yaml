id: encryption-intransit-disabled

info:
  name: RDS Encryption in Transit - Disabled
  author: DhiyaneshDK
  severity: high
  description: |
    Encryption in transit for Alibaba Cloud RDS is disabled, exposing data transmission to potential interception and unauthorized access.
  reference:
    - https://www.alibabacloud.com/help/en/rds/apsaradb-rds-for-mysql/configure-ssl-encryption-for-an-apsaradb-rds-for-mysql-instance
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RDS/enable-encryption-in-transit.html
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-rds

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for(let DBInstanceId of iterate(template.dbinstanceid)){
    set("instance", DBInstanceId)
    code(2)
  }


self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun rds DescribeDBInstances --region $region

    extractors:
      - type: json
        name: dbinstanceid
        internal: true
        json:
          - '.Items.DBInstance[].DBInstanceId'

  - engine:
      - sh
      - bash

    source: |
      aliyun rds DescribeDBInstanceSSL --DBInstanceId $dbinstanceid --region $region

    matchers:
      - type: word
        words:
          - '"SSLEnabled": "No"'

    extractors:
      - type: dsl
        dsl:
          - 'instance + " RDS Encryption in Transit is Disabled "'
# digest: 4b0a004830460221008975c054537a43ffe8ff1465de493660483ef4e72f1f6a18862dfa18b710ca13022100d7e92bcb5bf853223af4bb77099236fe3789fbd45bb45b226ae878ac38c089ae:922c64590222798bb761d5b6d8e72950