id: stack-policy-not-inuse

info:
  name: CloudFormation Stack Policy - Not In Use
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure your AWS CloudFormation stacks are using policies as a fail-safe mechanism in order to prevent accidental updates to stack resources.
  impact: |
    Lack of a CloudFormation Stack Policy allows unrestricted modifications to stack resources, increasing the risk of unintended or harmful changes.
  remediation: |
    Implement a CloudFormation Stack Policy to restrict updates to critical resources, defining explicit rules for which resources can be modified during stack updates.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/CloudFormation/cloudformation-stack-policy.html
    - http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/protect-stack-resources.html
  tags: cloud,devops,aws,amazon,cloudformation,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let StackSummaries of iterate(template.stacksummaries)){
    set("stacksummary", StackSummaries)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws cloudformation list-stacks --region $region --output json --query 'StackSummaries[*].StackName'

    extractors:
      - type: json
        name: stacksummaries
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws cloudformation get-stack-policy --region $region --stack-name $stacksummary --output json

    matchers:
      - type: word
        words:
          - 'StackPolicyBody'
          - 'Effect'
        negative: true

    extractors:
      - type: dsl
        dsl:
          - 'stacksummary + " No Stack Policy In Use"'
# digest: 4b0a00483046022100a746affd01686c4b233d818424db502ef0dd44c73af71e06408ad6643c49817b022100ec9758a323f48a9ec00e94eb8669ad58551fd8b2cbb8fc0d1ad835020645cd13:922c64590222798bb761d5b6d8e72950