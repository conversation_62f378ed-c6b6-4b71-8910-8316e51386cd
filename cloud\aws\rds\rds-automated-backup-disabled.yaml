id: rds-automated-backup-disabled

info:
  name: RDS Automated Backups - Disabled
  author: DhiyaneshDK
  severity: high
  description: |
    Ensure that your Amazon RDS database instances have automated backups enabled for point-in-time recovery.
  impact: |
    Data loss risk increases, as the RDS instance cannot be automatically restored to a previous state in case of failure or accidental deletion.
  remediation: |
    Enable automated backups for the RDS instance in the AWS Management Console, CLI, or API to ensure regular, automatic backups are created and retained.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/RDS/rds-automated-backups-enabled.html
    - http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_WorkingWithAutomatedBackups.html
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DBClusterIdentifier of iterate(template.dbclusters)){
    set("dbcluster", DBClusterIdentifier)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --query 'DBInstances[*].DBInstanceIdentifier' --output json

    extractors:
      - type: json
        name: dbclusters
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws rds describe-db-instances --region $region --db-instance-identifier $dbcluster --query 'DBInstances[*].BackupRetentionPeriod' --output json

    matchers:
      - type: word
        words:
          - '0'

    extractors:
      - type: dsl
        dsl:
          - 'dbcluster + " RDS Automated Backups is Disabled"'
# digest: 4b0a00483046022100bbabde54e7c69ff58d74f3607515133c7bae3f6fc8f39b003a6338e746058a54022100af2c3fd24f23f2961b7734b6ab86da6edd685bb715b0aa3533a2819cb8566792:922c64590222798bb761d5b6d8e72950