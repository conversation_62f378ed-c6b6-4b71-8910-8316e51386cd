id: scheduled-vulnscan-disabled

info:
  name: Scheduled Vulnerability Scan - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    The automated vulnerability scanning feature is turned off, preventing regular checks for security weaknesses. This increases the risk of undetected vulnerabilities in the system.
  reference:
    - https://www.alibabacloud.com/help/en/security-center/user-guide/scan-for-vulnerabilities
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-SecurityCenter/enable-scheduled-vulnerability-scan.html
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,security-center

variables:
  region: "cn-hangzhou"

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun sas DescribeVulConfig --region $region

    matchers:
      - type: word
        name: policy
        words:
          - '"Config": "off"'

    extractors:
      - type: dsl
        dsl:
          - '"Scheduled Vulnerability Scan is Disabled "'
# digest: 490a0046304402206c02830649fd1a4625f43a0339e4d3b03efda89840d274c8b94a54928878845d02207b010d79b6248d8df3e457e7550bafc969967a0f87d0b491911c9db87c527e5b:922c64590222798bb761d5b6d8e72950