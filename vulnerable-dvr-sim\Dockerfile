FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    busybox \
    netcat-openbsd \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create DVR directory structure
RUN mkdir -p /mnt/mtd \
    && mkdir -p /var/log/dvr \
    && mkdir -p /nfsdir/language \
    && mkdir -p /dvr/web

# Copy vulnerable DVR simulation
COPY cross_web_server.py /dvr/cross_web_server.py
COPY dvr_web_files/ /dvr/web/
COPY start_dvr.sh /dvr/start_dvr.sh

# Make scripts executable
RUN chmod +x /dvr/start_dvr.sh

# Create DVR user (simulate embedded system)
RUN useradd -r -s /bin/false dvr

# Set working directory
WORKDIR /dvr

# Expose DVR ports
EXPOSE 81 82 8000

# Start DVR services
CMD ["/dvr/start_dvr.sh"]
