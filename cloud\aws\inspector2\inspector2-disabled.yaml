id: inspector2-disabled

info:
  name: Amazon Inspector 2 - Disabled
  author: <PERSON><PERSON>yaneshDK
  severity: info
  description: |
    Ensure that the new version of Amazon Inspector is enabled in order to help you improve the security and compliance of your AWS cloud environment.
  impact: |
    Amazon Inspector 2 disabled increases the risk of unaddressed vulnerabilities in your EC2 instances, Lambda functions, and container images, leaving your environment exposed to potential security threats.
  remediation: |
    Enable Amazon Inspector 2 to automatically scan for vulnerabilities in EC2 instances, Lambda functions, and container images by using the AWS Management Console or using the AWS CLI.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/Inspector2/enable-amazon-inspector2.html
    - https://aws.amazon.com/about-aws/whats-new/2021/11/amazon-inspector-continual-vulnerability-management/
  tags: cloud,devops,aws,amazon,inspector2,aws-cloud-config

variables:
  region: "us-west-2"

self-contained: true

code:
  - engine:
      - sh
      - bash

    source: |
      aws inspector2 batch-get-account-status --region $region --query 'accounts[*].[accountId,state.status]' --output json

    matchers:
      - type: word
        words:
          - '"DISABLED"'

    extractors:
      - type: dsl
        dsl:
          - '"Amazon Inspector 2 " + region + " is Disabled"'
# digest: 4b0a00483046022100c16653b788646224d60b817395ec75175f014760bcc5a4f0fb709ebb84687648022100f33fb2b94181b6261f5e31d99a8e9624bdb8aaddfea48485ca291a9fb0f883b1:922c64590222798bb761d5b6d8e72950