id: malware-protection-disabled

info:
  name: GuardDuty Malware Protection - Disabled
  author: DhiyaneshDK
  severity: info
  description: |
    Ensure that the Malware Protection feature is enabled for your Amazon GuardDuty detectors.
  impact: |
    GuardDuty Malware Protection disabled increases the risk of undetected malware threats on EBS volumes, potentially leading to data compromise or system breaches.
  remediation: |
    Enable GuardDuty Malware Protection by configuring the feature in the GuardDuty console or using the AWS CLI, to scan EBS volumes for malware and ensure proactive threat detection.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/GuardDuty/enable-malware-protection.html
    - https://docs.aws.amazon.com/guardduty/latest/ug/malware-protection.html
  tags: cloud,devops,aws,amazon,guardduty,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DetectorIds of iterate(template.detectors)){
    set("detector", DetectorIds)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws guardduty list-detectors --region $region --query 'DetectorIds' --output json

    extractors:
      - type: json
        name: detectors
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws guardduty get-detector --region $region --detector-id "$detector" --query 'DataSources.MalwareProtection.ScanEc2InstanceWithFindings.EbsVolumes.Status' --output json

    matchers:
      - type: word
        words:
          - "DISABLED"

    extractors:
      - type: dsl
        dsl:
          - '"GuardDuty Malware Protection " + detector + " is Disabled"'
# digest: 4a0a0047304502210084ac2e28bc6bc93fd95ea79e10a8c8616131834514a20b020579d64a8bcf5db202202781b082dad0a37bed2c28670cf8f4d3d780b40862d3f13f2fff1395f1bd77e8:922c64590222798bb761d5b6d8e72950