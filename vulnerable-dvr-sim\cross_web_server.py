#!/usr/bin/env python3
"""
Vulnerable Cross Web Server Simulation for CVE-2025-34036
This simulates the vulnerable TVT DVR Cross Web Server for testing purposes.
DO NOT USE IN PRODUCTION - FOR TESTING ONLY
"""

import os
import re
import subprocess
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, unquote
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VulnerableDVRHandler(BaseHTTPRequestHandler):
    """Simulates the vulnerable Cross Web Server behavior"""
    
    def log_message(self, format, *args):
        """Override to use our logger"""
        logger.info(f"{self.address_string()} - {format % args}")
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            path = unquote(self.path)
            logger.info(f"Received request: {path}")
            
            # Check for language extraction vulnerability
            if self.handle_language_extraction(path):
                return
            
            # Handle normal DVR web interface requests
            self.handle_normal_request(path)
            
        except Exception as e:
            logger.error(f"Error handling request: {e}")
            self.send_error(500, "Internal Server Error")
    
    def handle_language_extraction(self, path):
        """Handle the vulnerable language extraction functionality"""
        # Pattern: /language/[PAYLOAD]/...
        language_pattern = r'^/language/([^/]+)/'
        match = re.match(language_pattern, path)
        
        if match:
            language_param = match.group(1)
            logger.info(f"Language extraction request detected: {language_param}")
            
            # Simulate the vulnerable tar command execution
            # This is where the command injection occurs
            try:
                # Construct the vulnerable command (simulated)
                # Original: tar -zxf /mnt/mtd/WebSites/language.tar.gz [language]/* -C /nfsdir/language/
                command = f"tar -zxf /mnt/mtd/WebSites/language.tar.gz {language_param}/* -C /nfsdir/language/"
                
                logger.warning(f"VULNERABLE: Executing command: {command}")
                
                # For safety, we'll simulate command execution without actually running dangerous commands
                # But we'll execute safe commands to demonstrate the vulnerability
                if self.is_safe_command(language_param):
                    # Execute the command (only safe ones for demonstration)
                    result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=10)
                    logger.info(f"Command executed with return code: {result.returncode}")
                    
                    # Send response
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.send_header('Server', 'Cross Web Server')
                    self.end_headers()
                    
                    response = f"""
                    <html>
                    <head><title>Language Extraction</title></head>
                    <body>
                    <h1>TVT DVR - Language Extraction</h1>
                    <p>Language parameter processed: {language_param}</p>
                    <p>Command executed successfully</p>
                    </body>
                    </html>
                    """
                    self.wfile.write(response.encode())
                    return True
                else:
                    logger.warning(f"Potentially dangerous command blocked: {language_param}")
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.send_header('Server', 'Cross Web Server')
                    self.end_headers()
                    self.wfile.write(b"Language extraction processed")
                    return True
                    
            except subprocess.TimeoutExpired:
                logger.error("Command execution timed out")
                self.send_error(500, "Command timeout")
                return True
            except Exception as e:
                logger.error(f"Command execution error: {e}")
                self.send_error(500, "Command execution failed")
                return True
        
        return False
    
    def is_safe_command(self, param):
        """Check if the command parameter is safe for demonstration"""
        # Allow safe commands for testing (echo, basic file operations)
        safe_patterns = [
            r'Swedish\$\{IFS\}&&echo\$\{IFS\}[A-Za-z0-9]+>[A-Za-z0-9]+\.txt&&tar\$\{IFS\}',
            r'Swedish\$\{IFS\}&&rm\$\{IFS\}[A-Za-z0-9]+\.txt&&tar\$\{IFS\}'
        ]
        
        for pattern in safe_patterns:
            if re.match(pattern, param):
                return True
        
        # Block potentially dangerous commands
        dangerous_keywords = ['rm -rf', 'dd', 'mkfs', 'format', 'shutdown', 'reboot', 'passwd']
        for keyword in dangerous_keywords:
            if keyword in param.lower():
                return False
        
        return True
    
    def handle_normal_request(self, path):
        """Handle normal DVR web interface requests"""
        if path == '/' or path == '/index.html':
            self.send_dvr_homepage()
        elif path.startswith('/../../../../../../../mnt/mtd/'):
            # Handle file access requests (for vulnerability verification)
            self.handle_file_access(path)
        else:
            self.send_error(404, "Not Found")
    
    def send_dvr_homepage(self):
        """Send the DVR homepage"""
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.send_header('Server', 'Cross Web Server')
        self.end_headers()
        
        homepage = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>TVT DVR System</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .header { background: #2c3e50; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .info { background: #ecf0f1; padding: 15px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>TVT Digital Video Recorder</h1>
                <p>Cross Web Server - CCTV Management System</p>
            </div>
            <div class="content">
                <div class="info">
                    <h3>System Information</h3>
                    <p><strong>Model:</strong> TVT-TD2316TS-HC</p>
                    <p><strong>Firmware:</strong> V4.02.R11.00000000.10001.131201</p>
                    <p><strong>Server:</strong> Cross Web Server</p>
                    <p><strong>Status:</strong> Online</p>
                </div>
                <div class="info">
                    <h3>Features</h3>
                    <ul>
                        <li>16 Channel HD Recording</li>
                        <li>Remote Access</li>
                        <li>Multi-language Support</li>
                        <li>Mobile App Integration</li>
                    </ul>
                </div>
            </div>
        </body>
        </html>
        """
        self.wfile.write(homepage.encode())
    
    def handle_file_access(self, path):
        """Handle file access requests (for vulnerability verification)"""
        # Extract the actual file path
        file_match = re.search(r'/mnt/mtd/([^/\s]+)', path)
        if file_match:
            filename = file_match.group(1)
            file_path = f"/mnt/mtd/{filename}"
            
            logger.info(f"File access request: {file_path}")
            
            try:
                if os.path.exists(file_path):
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    self.send_response(200)
                    self.send_header('Content-type', 'text/plain')
                    self.send_header('Server', 'Cross Web Server')
                    self.end_headers()
                    self.wfile.write(content.encode())
                else:
                    self.send_error(404, "File not found")
            except Exception as e:
                logger.error(f"File access error: {e}")
                self.send_error(500, "File access error")
        else:
            self.send_error(400, "Invalid file path")

def start_server(port):
    """Start the vulnerable DVR server on specified port"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, VulnerableDVRHandler)
    logger.info(f"Starting Cross Web Server on port {port}")
    httpd.serve_forever()

if __name__ == "__main__":
    # Start servers on multiple ports (typical for DVR systems)
    ports = [81, 82, 8000]
    
    # Create /mnt/mtd directory if it doesn't exist
    os.makedirs('/mnt/mtd', exist_ok=True)
    
    threads = []
    for port in ports:
        thread = threading.Thread(target=start_server, args=(port,))
        thread.daemon = True
        thread.start()
        threads.append(thread)
    
    logger.info("All Cross Web Servers started")
    
    # Keep the main thread alive
    try:
        for thread in threads:
            thread.join()
    except KeyboardInterrupt:
        logger.info("Shutting down servers...")
