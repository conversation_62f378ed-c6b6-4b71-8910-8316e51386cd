id: efs-encryption-disabled

info:
  name: <PERSON><PERSON> Encryption - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that the data available on your Amazon EFS file systems is encrypted at rest in order to meet security and compliance requirements.
  impact: |
    Sensitive data transmitted or stored in Redis could be exposed, leading to potential data breaches or unauthorized access.
  remediation: |
    Enable encryption for AWS EFS by configuring encryption at rest in the EFS settings to protect data from unauthorized access.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/ElastiCache/in-transit-and-at-rest-encryption.html
    - https://docs.aws.amazon.com/efs/latest/ug/encryption.html
  tags: cloud,devops,aws,amazon,efs-encryption-disabled,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let FileSystemId of iterate(template.filesystemids)){
    set("filesystemid", FileSystemId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws efs describe-file-systems --region $region --output json --query 'FileSystems[*].FileSystemId'

    extractors:
      - type: json
        name: filesystemids
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws efs describe-file-systems --region $region --file-system-id $filesystemid --query 'FileSystems[*].Encrypted' --output json

    matchers:
      - type: word
        words:
          - "false"

    extractors:
      - type: dsl
        dsl:
          - 'filesystemid + " EFS Encryption is Disabled"'
# digest: 4b0a00483046022100d2125edc6d62626832f2d0740df2cf25b8e216c4dcc9a9408fbf86c76c32c609022100dbe0527c50f30c846242860d05b0b61d82fabbc5830f7878c81301bfa77efaae:922c64590222798bb761d5b6d8e72950