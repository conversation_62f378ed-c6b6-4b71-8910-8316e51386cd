/*
 * WooCommerce Ultimate Gift Card Admin Styles
 * Version: 2.6.0 (Vulnerable)
 * CVE-2024-8425 Test Environment
 */

.mwb_wgm_setting {
    display: block;
    margin: 10px 0;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.mwb_wgm_preview_container {
    max-width: 600px;
    margin: 20px auto;
    padding: 20px;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.mwb_wgm_upload_form {
    border: 2px dashed #ccc;
    padding: 40px;
    text-align: center;
    margin: 20px 0;
}

.mwb_wgm_upload_form:hover {
    border-color: #999;
    background: #fafafa;
}

.mwb_wgm_file_input {
    margin: 10px 0;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    width: 100%;
    max-width: 300px;
}

.mwb_wgm_submit_btn {
    background: #0073aa;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
}

.mwb_wgm_submit_btn:hover {
    background: #005a87;
}

.mwb_wgm_error {
    color: #d63638;
    background: #fcf0f1;
    border: 1px solid #d63638;
    padding: 10px;
    border-radius: 3px;
    margin: 10px 0;
}

.mwb_wgm_success {
    color: #00a32a;
    background: #f0f6fc;
    border: 1px solid #00a32a;
    padding: 10px;
    border-radius: 3px;
    margin: 10px 0;
}

/* Vulnerable indicator for testing */
.mwb_wgm_vulnerable_version {
    display: none;
    content: "CVE-2024-8425-vulnerable";
}
