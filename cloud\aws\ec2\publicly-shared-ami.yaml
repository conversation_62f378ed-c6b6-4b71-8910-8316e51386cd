id: publicly-shared-ami
info:
  name: Publicly Shared AMI
  author: princechaddha
  severity: medium
  description: |
    Checks if Amazon Machine Images (AMIs) are publicly shared, potentially exposing sensitive data.
  impact: |
    Public sharing of AMIs can lead to unauthorized access and compromise of sensitive information contained within these images.
  remediation: |
    Restrict AMI sharing to specific, trusted AWS accounts and ensure they are not publicly accessible.
  reference:
    - https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/sharingamis-explicit.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,ami,aws-cloud-config
variables:
  region: "us-east-1"

flow: |
  code(1)
  for(let AmiName of iterate(template.amis)){
    set("ami", AmiName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws ec2 describe-images --region $region --owners self --output json --query 'Images[*].ImageId' --output json

    extractors:
      - type: json
        name: amis
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws ec2 describe-images --region $region --image-ids $ami --owners self --query 'Images[*].Public'

    matchers:
      - type: word
        words:
          - "true"

    extractors:
      - type: dsl
        dsl:
          - 'ami + " AMI is publically shared"'
# digest: 4a0a004730450220079156e39eca92cef40132a9da44be38cfb477558fd0249172888db782cba85402210092d8436f349fa51e1a63172e942b54b96af4482e623628435ece05e81c560ba1:922c64590222798bb761d5b6d8e72950