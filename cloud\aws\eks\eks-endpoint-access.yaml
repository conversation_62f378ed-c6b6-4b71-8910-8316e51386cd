id: eks-endpoint-access

info:
  name: EKS Cluster Endpoint Public Access
  author: princechaddha
  severity: high
  description: |
    Ensure that your Amazon EKS cluster's Kubernetes API server endpoint is not publicly accessible from the Internet in order to avoid exposing private data and minimizing security risks.
  impact: |
    Publicly accessible EKS cluster endpoints expose your Kubernetes API server to potential unauthorized access and attacks, increasing the risk of data breaches and security compromises.
  remediation: |
    Configure the EKS cluster endpoint access to be private or restrict public access to specific IP addresses. Use VPC endpoints and security groups to control access to the Kubernetes API server.
  reference:
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/aws/EKS/endpoint-access.html
    - https://docs.aws.amazon.com/eks/latest/userguide/cluster-endpoint.html
  tags: cloud,devops,aws,amazon,eks,aws-cloud-config

variables:
  region: "us-east-1"

flow: |
  code(1)
  for(let clusterName of iterate(template.clusters)){
    set("cluster", clusterName)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws eks list-clusters --region $region --query 'clusters[]' --output json

    extractors:
      - type: json
        name: clusters
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
      aws eks describe-cluster --name $cluster --region $region --query 'cluster.resourcesVpcConfig' --output json

    matchers-condition: and
    matchers:
      - type: word
        words:
          - '"endpointPublicAccess": true'

      - type: word
        words:
          - '"0.0.0.0/0"'

    extractors:
      - type: dsl
        dsl:
          - '"EKS cluster " + cluster + " has public endpoint access enabled with unrestricted CIDR (0.0.0.0/0)"'
# digest: 4a0a004730450220449d0874682b6d4ed40f71819e03f437222ad9b8f6b8cc21fc17bca8fda57714022100b3b986adfc06443888f8dc7148a6721b2dce6beabce549e0b74aebd6835d3f80:922c64590222798bb761d5b6d8e72950