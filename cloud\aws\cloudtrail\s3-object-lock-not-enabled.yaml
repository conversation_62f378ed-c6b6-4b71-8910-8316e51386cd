id: s3-object-lock-not-enabled
info:
  name: CloudTrail S3 Object Lock
  author: princechaddha
  severity: medium
  description: |
    Ensure Amazon CloudTrail S3 buckets have Object Lock enabled to prevent log deletion and ensure regulatory compliance.
  impact: |
    Without Object Lock, S3 objects such as CloudTrail logs can be deleted, compromising audit trails and violating compliance requirements.
  remediation: |
    Enable S3 Object Lock in Governance mode with a retention period that meets your compliance requirements for CloudTrail S3 buckets.
  reference:
    - https://docs.aws.amazon.com/AmazonS3/latest/dev/object-lock-overview.html
  metadata:
    max-request: 3
  tags: cloud,devops,aws,amazon,s3,aws-cloud-config
variables:
  region: "ap-south-1"

flow: |
  code(1)
  for(let CloudTrail of iterate(template.cloudtrailname)){
    set("trail", CloudTrail)
    code(2)
    for(let BucketNames of iterate(template.buckets)){
      set("bucket", BucketNames)
      code(3)
    }
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws cloudtrail list-trails --region $region --query 'Trails[*].Name' --output json

    extractors:
      - type: json
        name: cloudtrailname
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
      aws cloudtrail describe-trails --region $region --trail-name-list $trail --query 'trailList[*].S3BucketName'

    extractors:
      - type: json
        name: buckets
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
      aws s3api get-object-lock-configuration --bucket $bucket --query 'ObjectLockConfiguration.ObjectLockEnabled' --output json

    matchers:
      - type: word
        part: code_3_stderr
        words:
          - 'ObjectLockConfigurationNotFoundError'

    extractors:
      - type: dsl
        dsl:
          - '"The Object Lock feature is not enabled for the S3 bucket associated with the CloudTrail trail " + trail'
# digest: 4a0a0047304502206a9bee19bdcefd4c05c149a0aa0da4c07b77a768fb208897687b8e3c92425e510221008e6b2c80c6e79734c084560ad8640df0b977475c6561d4958bf7219e1a6d3fc7:922c64590222798bb761d5b6d8e72950