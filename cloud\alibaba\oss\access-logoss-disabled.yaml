id: access-logoss-disabled

info:
  name: Access Logging for OSS Buckets - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Disabling access logging for OSS buckets reduces the ability to monitor and audit access, increasing the risk of undetected unauthorized activity.
  reference:
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-OSS/enable-bucket-access-logging.html
    - https://www.alibabacloud.com/help/en/oss/user-guide/getting-started
    - https://www.alibabacloud.com/help/en/oss/user-guide/enable-logging
  metadata:
    max-request: 2
    verified: true
  tags: cloud,devops,aliyun,alibaba,aliyun-cloud-config,alibaba-oss

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for (let BucketName of iterate(template.bucketname)) {
    set("bucket", BucketName)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash

    source: |
      aliyun oss ls --region $region

    extractors:
      - type: regex
        name: bucketname
        internal: true
        regex:
          - 'oss://([a-zA-Z0-9-]+)'

  - engine:
      - sh
      - bash

    source: |
      aliyun oss logging --method get $bucket --region $region

    matchers:
      - type: word
        words:
          - '<LoggingEnabled>'
          - '<TargetBucket></TargetBucket>'
        condition: and

    extractors:
      - type: dsl
        dsl:
          - 'bucket + " Access Logging for OSS Buckets is Disabled "'
# digest: 490a0046304402202c7db531327c5aa97814ff449d8796b0520264e13d531fcebdf3e90d64252cb60220045d1db959712237219b389022dbaabf1bcb023ca5151e5539546aad2819d8c7:922c64590222798bb761d5b6d8e72950