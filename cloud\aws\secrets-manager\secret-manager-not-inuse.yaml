id: secret-manager-not-inuse

info:
  name: Secrets Manager Not In Use
  author: D<PERSON>yaneshDK
  severity: info
  description: |
    Ensure that Amazon Secrets Manager service is used in your AWS account to manage access credentials (i.e. secrets) such as API keys, OAuth tokens and database credentials.
  impact: |
    AWS Secrets Manager improves security by securely managing and rotating sensitive credentials, reducing the risk of exposure and unauthorized access.
  remediation: |
    Ensure AWS Secrets Manager is used to securely store, manage, and rotate sensitive credentials such as API keys, database passwords, and tokens, and remove hard-coded secrets from applications.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/SecretsManager/secrets-manager-in-use.html
    - https://docs.aws.amazon.com/secretsmanager/latest/userguide/intro.html
  tags: cloud,devops,aws,amazon,secret-manager,aws-cloud-config

variables:
  region: "us-west-2"

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws secretsmanager list-secrets --region $region --query 'SecretList[*].Name' --output json

    matchers:
      - type: word
        words:
          - '[]'

    extractors:
      - type: dsl
        dsl:
          - '"Secrets Manager Not In Use" + region + " AWS region"'
# digest: 4a0a004730450220417b4366f7c291bf10ae4aa09013d6fa750065e2c8217de9cc58c14d4aad1f8a022100b55bc14bdd0f80bb5a5f0fcf55761e31efc38b6b6a0717354c6dcbe2b435875c:922c64590222798bb761d5b6d8e72950