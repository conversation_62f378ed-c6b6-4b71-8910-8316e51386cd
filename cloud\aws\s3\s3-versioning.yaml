id: s3-versioning
info:
  name: S3 Bucket Versioning not Enabled
  author: princechaddha
  severity: low
  description: |
    Verifies that Amazon S3 buckets have object versioning enabled, providing a safeguard for recovering overwritten or deleted objects
  reference:
    - https://docs.aws.amazon.com/AmazonS3/latest/userguide/manage-versioning-examples.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,s3,aws-cloud-config

flow: |
  code(1)
  for(let bucketName of iterate(template.buckets)){
    set("bucket", bucketName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws s3api list-buckets --query 'Buckets[*].Name'

    extractors:
      - type: json # type of the extractor
        internal: true
        name: buckets
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws s3api get-bucket-versioning --bucket $bucket --query 'Status'

    matchers:
      - type: word
        words:
          - "null"
          - "Suspended"

    extractors:
      - type: dsl
        dsl:
          - '"Versioning is not enabled for S3 Bucket " + bucket'
# digest: 4b0a00483046022100a74b2b0fc719e85dc4f0eecfdcb547ce2010549e0b2f59604b1b25d880a1b945022100efac9d12807b1be9182827d7d24700caa711b875b12f67a9a147458c4d2d9715:922c64590222798bb761d5b6d8e72950