id: cloudfront-security-policy

info:
  name: CloudFront Security Policy
  author: D<PERSON>yaneshDK
  severity: medium
  description: |
    Ensure that your Amazon CloudFront distributions are using a security policy with minimum TLSv1.2 or TLSv1.3 and appropriate security ciphers for HTTPS viewer connections.
  impact: |
    Failing to use a security policy with a minimum of TLSv1.2 or TLSv1.3 and appropriate ciphers for HTTPS viewer connections in CloudFront can expose sensitive data to interception and reduce the overall security of your application.
  remediation: |
    Configure your Amazon CloudFront distributions to use a security policy that enforces a minimum of TLSv1.2 or TLSv1.3 and specifies secure ciphers for HTTPS viewer connections.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/CloudFront/security-policy.html
    - https://aws.amazon.com/about-aws/whats-new/2017/09/amazon-cloudfront-now-lets-you-select-a-security-policy-with-minimum-tls-v1_1-1_2-and-security-ciphers-for-viewer-connections/
  tags: cloud,devops,aws,amazon,cloudfront,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DistributionListItemsId of iterate(template.distributions)){
    set("distribution", DistributionListItemsId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash

    source: |
      aws cloudfront list-distributions --output table --query 'DistributionList.Items[*].Id' --region $region --output json

    extractors:
      - type: json
        name: distributions
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws cloudfront get-distribution --id $distribution --query 'Distribution.DistributionConfig.ViewerCertificate.MinimumProtocolVersion' --region $region --output json

    matchers:
      - type: word
        words:
          - '"TLSv1"'
          - '"TLSv1_2016"'
          - '"TLSv1.1_2016"'
          - '"TLSv1.2_2018"'
          - '"TLSv1.2_2019"'

    extractors:
      - type: dsl
        dsl:
          - '"CloudFront Uses Insecure Protocols " + distribution'
# digest: 4b0a004830460221008c2f205519658e0b52736eb47e67a7a80b97db0fbf144406765b715caa5671070221008aa467383b6f43ebd7f7d4fa6c6f609ba95fafa2790aace7099570b6c5bcd980:922c64590222798bb761d5b6d8e72950