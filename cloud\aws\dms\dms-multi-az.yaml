id: dms-multi-az

info:
  name: DMS Multi-AZ Not Enabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that your Amazon Database Migration Service (DMS) replication instances are using Multi-AZ deployment configurations to provide High Availability (HA) through automatic failover to standby replicas in the event of a failure such as an Availability Zone (AZ) outage, an internal hardware or network outage, a software failure or in case of a planned maintenance session
  impact: |
    Not enabling Multi-AZ for Database Migration Service can lead to increased downtime and data loss risks during outages, compromising the availability and reliability of your database operations.
  remediation: |
    Enable Multi-AZ support for your Database Migration Service to enhance availability and resilience, ensuring automatic failover and reducing downtime during outages.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/DMS/multi-az.html
    - https://docs.aws.amazon.com/cli/latest/reference/dms/describe-replication-instances.html
  tags: cloud,devops,aws,amazon,dms,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let ReplicationInstances of iterate(template.replications)){
    set("replication", ReplicationInstances)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws dms describe-replication-instances --region $region --query "ReplicationInstances[*].ReplicationInstanceArn" --output json

    extractors:
      - type: json
        name: replications
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws dms describe-replication-instances --region $region --filters Name=replication-instance-arn,Values=$replication --query "ReplicationInstances[*].MultiAZ" --output json

    matchers:
      - type: word
        words:
          - "false"

    extractors:
      - type: dsl
        dsl:
          - '"DMS Multi-AZ  " + replication + " is not enabled"'
# digest: 4b0a00483046022100c67cd0c33238f95b20d5912c1b8800aa69940bb716dd35f2bb8ca66aea1c4ba80221009c7433225fd40509b4a85e2fc696eb64d692e25a530dfbb8b47795c3439d5c46:922c64590222798bb761d5b6d8e72950