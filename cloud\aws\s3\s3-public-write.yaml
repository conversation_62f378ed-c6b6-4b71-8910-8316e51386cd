id: s3-public-write
info:
  name: S3 Bucket with Public WRITE Access
  author: princechaddha
  severity: critical
  description: |
    Checks if Amazon S3 buckets are secured against public WRITE access, preventing unauthorized modifications to access control permissions.
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/s3api/get-bucket-acl.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,s3,aws-cloud-config

flow: |
  code(1)
  for(let bucketName of iterate(template.buckets)){
    set("bucket", bucketName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws s3api list-buckets --query 'Buckets[*].Name'

    extractors:
      - type: json # type of the extractor
        internal: true
        name: buckets
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws s3api get-bucket-acl --bucket $bucket --query 'Grants[?(Grantee.URI==`http://acs.amazonaws.com/groups/global/AllUsers`)]'

    matchers:
      - type: word
        words:
          - '"Permission": "WRITE"'

    extractors:
      - type: dsl
        dsl:
          - '"The S3 bucket " + bucket +" have public WRITE access"'
# digest: 4b0a00483046022100d99877590e07d7434dce5334e556640c79137c44c1364159f577d1cb4e7ea6c60221009aaa5f2b574545b10e528d89cc49c9d1c7b6ebbfe35467b3d948ec9a7a36b935:922c64590222798bb761d5b6d8e72950