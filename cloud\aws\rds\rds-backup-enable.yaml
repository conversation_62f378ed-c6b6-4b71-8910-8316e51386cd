id: rds-backup-enable
info:
  name: RDS Automated Backup Check
  author: princechaddha
  severity: high
  description: |
    Ensure that your Amazon RDS database instances have automated backups enabled for point-in-time recovery.
  impact: |
    Lack of automated backups can lead to data loss in case of accidental deletion or database corruption.
  remediation: |
    Enable automated backups for RDS instances by setting the backup retention period to a value other than 0.
  reference:
    - https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_WorkingWithAutomatedBackups.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config
variables:
  region: "ap-northeast-1"

flow: |
  code(1)
  for(let DBInstances of iterate(template.instances)){
    set("db", DBInstances)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --output json --query 'DBInstances[*].DBInstanceIdentifier'

    extractors:
      - type: json
        name: instances
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws rds describe-db-instances --region $region --db-instance-identifier $db --query 'DBInstances[*].BackupRetentionPeriod'

    matchers:
      - type: word
        words:
          - '0'

    extractors:
      - type: dsl
        dsl:
          - '"Automated backups are not enabled for " + db + " RDS database instance"'
# digest: 490a004630440220268da23a706ca9cdb3255c733067470a672223cb5e38b2630a9a1dcc0679f5d10220714233c214952bbedd861c5dcd55086a08fbe70db5dd9a8951847e58a9addfed:922c64590222798bb761d5b6d8e72950