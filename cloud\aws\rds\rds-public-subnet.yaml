id: rds-public-subnet
info:
  name: RDS Instance Private Subnet
  author: princechaddha
  severity: high
  description: |
    Ensure Amazon RDS database instances are not provisioned in VPC public subnets to avoid direct Internet exposure.
  impact: |
    RDS instances in public subnets can be directly accessed from the Internet, increasing the risk of unauthorized access and potential data breaches.
  remediation: |
    Migrate RDS instances to private subnets within the VPC and ensure proper network ACLs and security group settings are in place to restrict access.
  reference:
    - https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_VPC.WorkingWithRDSInstanceinaVPC.html#USER_VPC.Subnets
  metadata:
    max-request: 3
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config
variables:
  region: "ap-northeast-1"

flow: |
  code(1)
  for(let DBInstances of iterate(template.instances)){
    set("db", DBInstances)
    code(2)
    for(let SubnetNames of iterate(template.subnets)){
      set("subnet", SubnetNames)
      code(3)
    }
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --output json --query 'DBInstances[*].DBInstanceIdentifier'

    extractors:
      - type: json
        name: instances
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --db-instance-identifier $db --query 'DBInstances[*].DBSubnetGroup.Subnets[*].SubnetIdentifier[]'

    extractors:
      - type: json
        name: subnets
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
      aws ec2 describe-route-tables --region $region --filters "Name=association.subnet-id,Values=$subnet" --query 'RouteTables[*].Routes[]'

    matchers:
      - type: word
        words:
          - 'igw-'
          - '0.0.0.0/0'

    extractors:
      - type: dsl
        dsl:
          - 'db + " RDS instance is setup within a public subnet"'
# digest: 4a0a004730450220042bf2ca32e64d158855abce8f67ff4cbf0743f89844a322a93a284bed4a0dc5022100bd5c6d811ebd2949902dcdb47f454319e384cc1d8590253144c25698d903b185:922c64590222798bb761d5b6d8e72950