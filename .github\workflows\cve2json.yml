name: 📝 CVE JSON Metadata

on:
  push:
    branches:
      - main
    paths:
      - '**/cves/**'
  workflow_dispatch:

jobs:
  cve2json:
    runs-on: ubuntu-latest
    if: github.repository == 'projectdiscovery/nuclei-templates'
    steps:
      - uses: actions/checkout@v5
      - uses: projectdiscovery/actions/setup/go@v1
        with:
          go-version: 'stable'
      - run: go run main.go $GITHUB_WORKSPACE/http/cves/,$GITHUB_WORKSPACE/network/cves/,$GITHUB_WORKSPACE/javascript/cves/ $GITHUB_WORKSPACE/cves.json
        working-directory: .github/scripts/yaml2json
      - run: md5sum cves.json | cut -d' ' -f1 > cves.json-checksum.txt
      - uses: projectdiscovery/actions/setup/git@v1
      - uses: projectdiscovery/actions/commit@v1
        with:
          files: 'cves.json*'
          message: 'chore: generate CVEs metadata 🤖'
      - name: Push changes
        run: |
          git pull origin $GITHUB_REF --rebase
          git push origin $GITHUB_REF
