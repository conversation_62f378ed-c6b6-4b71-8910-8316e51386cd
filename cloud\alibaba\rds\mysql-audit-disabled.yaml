id: mysql-audit-disabled

info:
  name: MySQL Database Instances - SQL Auditing Disabled
  author: DhiyaneshDK
  severity: high
  description: |
    SQL auditing is disabled on the MySQL database instances, meaning activities such as user queries and connection events are not logged. This may hinder the ability to track database activity, detect suspicious behavior, and comply with security auditing requirements.
  reference:
    - https://www.alibabacloud.com/help/en/rds/apsaradb-rds-for-mysql/use-the-sql-explorer-and-audit-feature-on-an-apsaradb-rds-for-mysql-instance
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RDS/enable-mysql-audit-logs.html
  metadata:
    max-request: 2
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-rds

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for(let DBInstanceId of iterate(template.dbinstanceid)){
    set("instance", DBInstanceId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun rds DescribeDBInstances --Engine MySQL --region $region

    extractors:
      - type: json
        name: dbinstanceid
        internal: true
        json:
          - '.Items.DBInstance[].DBInstanceId'

  - engine:
      - sh
      - bash

    source: |
      aliyun rds DescribeSQLCollectorPolicy --DBInstanceId $dbinstanceid --region $region

    matchers:
      - type: word
        words:
          - '"SQLCollectorStatus": "Disabled"'

    extractors:
      - type: dsl
        dsl:
          - 'instance + " MySQL Database Instances SQL Auditing Disabled "'
# digest: 490a0046304402205a1185f2c0c30f07ffd9e058302d352539f591ef9f9b70d138ed10ff3214bf11022036b2bfdece214484a0fea82769f3b63f634ee1118a3681191aed816759316068:922c64590222798bb761d5b6d8e72950