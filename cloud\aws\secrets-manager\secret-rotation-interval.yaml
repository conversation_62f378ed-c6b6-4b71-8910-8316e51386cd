id: secret-rotation-interval

info:
  name: Secret Rotation Interval
  author: <PERSON><PERSON>yaneshDK
  severity: medium
  description: |
    Ensure that the rotation interval for your AWS Secrets Manager secrets is configured to meet security and compliance requirements.
  impact: |
    Disabling secret rotation intervals increases the risk of long-term exposure to compromised credentials, making the system vulnerable to unauthorized access.
  remediation: |
    Enable automatic secret rotation in AWS by configuring AWS Secrets Manager with a defined rotation interval (e.g., every 30 days) and using Lambda functions to automate the rotation process, ensuring credentials are regularly updated and secure.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/SecretsManager/rotation-interval.html
    - https://docs.aws.amazon.com/cli/latest/reference/secretsmanager/rotate-secret.html
  tags: cloud,devops,aws,amazon,secret-manager,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let SecretListName of iterate(template.secrets)){
    set("secretlist", SecretListName)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws secretsmanager list-secrets --region $region --query 'SecretList[*].Name' --output json

    extractors:
      - type: json
        name: secrets
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws secretsmanager describe-secret --region $region --secret-id $secretlist --query 'RotationRules.AutomaticallyAfterDays'

    matchers:
      - type: word
        words:
          - "false"

    extractors:
      - type: dsl
        dsl:
          - '"Secret Rotation Interval  " + secretlist + " is not defined"'
# digest: 4a0a00473045022035d594b724eb24fcd529c9ded97979878730061ca3d889f92f720560f8e023b802210082500861e2cb751d3b7625b7376537c4de1aea0a8d6cc55f16e733d3849925c8:922c64590222798bb761d5b6d8e72950