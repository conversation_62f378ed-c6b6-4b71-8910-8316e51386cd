#!/bin/bash

echo "Starting TVT DVR Simulation for CVE-2025-34036"
echo "=============================================="

# Create necessary directories
mkdir -p /mnt/mtd/WebSites
mkdir -p /nfsdir/language
mkdir -p /var/log/dvr

# Create a dummy language.tar.gz file
echo "Creating dummy language archive..."
cd /mnt/mtd/WebSites
echo "Dummy language file" > language.txt
tar -czf language.tar.gz language.txt
rm language.txt

# Set permissions
chmod 755 /mnt/mtd
chmod 755 /nfsdir/language

# Log startup
echo "$(date): DVR system starting up" >> /var/log/dvr/system.log
echo "$(date): Cross Web Server initializing" >> /var/log/dvr/system.log

# Display system info
echo "DVR Model: TVT-TD2316TS-HC"
echo "Firmware: V4.02.R11.00000000.10001.131201"
echo "Cross Web Server: Enabled"
echo "Ports: 81, 82, 8000"

# Start the vulnerable Cross Web Server
echo "Starting Cross Web Server..."
python3 /dvr/cross_web_server.py
