id: ack-cluster-network-policies-missing

info:
  name: Cluster Support for Network Policies - Missing
  author: ritikchaddha
  severity: medium
  description: |
    Ensure that your ACK clusters are using Kubernetes network policies to implement secure policy-based access control. Container Service for Kubernetes (ACK) employs the Terway network plugin to enforce network policies at the cluster level.
  reference:
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-ACK/enable-network-policy-support.html
  metadata:
    max-request: 2
    verified: true
  tags: cloud,devops,aliyun,alibaba,aliyun-cloud-config,ack

variables:
  region: "cn-hangzhou"

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun cs GET /clusters --header "Content-Type=application/json;" --body "{}"

    matchers-condition: and
    matchers:
      - type: regex
        regex:
          - '"Network"\s*:\s*"'

      - type: word
        words:
          - '"Network": "terway-eniip"'
        negative: true

    extractors:
      - type: dsl
        dsl:
          - '"Cluster is not using the plugin to enforce network policies"'
# digest: 490a004630440220131fa00233007ecfe8f70fc500b4efa66999ba6f1eb7d2987484c56ef13075dc02203e74e8da876a147c37d6de2a89c5995d52ca2933f9ec2eb473122fe32656a198:922c64590222798bb761d5b6d8e72950