id: unrestricted-rdp-access

info:
  name: Unrestricted - RDP Access
  author: DhiyaneshDK
  severity: high
  description: |
    Unrestricted RDP (Remote Desktop Protocol) access allows anyone on the internet to attempt to connect to your instance using port 3389, which is commonly used for RDP. This can expose the instance to potential brute force attacks, unauthorized access attempts, and exploitation of vulnerabilities in the RDP service.
  reference:
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-ECS/unrestricted-rdp-access.html
    - https://www.alibabacloud.com/help/en/ecs/use-cases/best-practices-of-the-security-group
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,ecs

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for(let SecurityGroupId of iterate(template.securitygroup)){
    set("securitygroup", SecurityGroupId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun ecs DescribeSecurityGroups --RegionId $region

    extractors:
      - type: json
        name: securitygroup
        internal: true
        json:
          - '.SecurityGroups.SecurityGroup[].SecurityGroupId'

  - engine:
      - sh
      - bash
    source: |
      aliyun ecs DescribeSecurityGroupAttribute --SecurityGroupId "$securitygroup" --Direction ingress --RegionId $region

    matchers-condition: and
    matchers:
      - type: word
        words:
          - '"IpProtocol": "TCP"'
          - '"PortRange": "3389/3389"'
          - '"SourceCidrIp": "0.0.0.0/0"'
        condition: and

    extractors:
      - type: dsl
        dsl:
          - 'securitygroup + " contains Unrestricted RDP Access"'
# digest: 4b0a0048304602210083f03472a0f1275e78b08fc4b68bde30bca25b86d874e0b7f53691e7259a9574022100aff7788972912f8e416e28adb1e05a94f601b3c5904ac26e5ad79f69ed46cc39:922c64590222798bb761d5b6d8e72950