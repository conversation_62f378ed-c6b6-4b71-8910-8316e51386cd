name: 🤖 TemplateMan

on:
  workflow_dispatch:

jobs:
  templateman:
    runs-on: ubuntu-latest
    if: github.repository == 'projectdiscovery/nuclei-templates'
    steps:
      - uses: actions/checkout@v5
      - uses: projectdiscovery/actions/setup/templateman@v1
        with:
          token: '${{ secrets.PDTEAMX_CLASSIC_PAT }}'
      - run: tmc -mr -e -at <<< "$(pwd)"
      - uses: projectdiscovery/actions/setup/git@v1
      - uses: projectdiscovery/actions/commit@v1
        with:
          message: 'chore: update TemplateMan 🤖'
      - name: Push changes
        run: |
          git pull origin $GITHUB_REF --rebase
          git push origin $GITHUB_REF
