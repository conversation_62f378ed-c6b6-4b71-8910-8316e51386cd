id: iam-access-analyzer
info:
  name: IAM Access Analyzer is not Used
  author: princechaddha
  severity: medium
  description: |
    Checks if Amazon IAM Access Analyzer is active for identifying unsolicited access risks in AWS resources
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/accessanalyzer/list-analyzers.html
  tags: cloud,devops,aws,amazon,iam,aws-cloud-config

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws accessanalyzer list-analyzers --query 'analyzers[*].arn'

    matchers:
      - type: word
        words:
          - "[]"

    extractors:
      - type: dsl
        dsl:
          - '"IAM Access Analyzer is not Used in your AWS account"'
# digest: 490a0046304402205548643793bb1c98150ed51f736af1d7cfa1896b5559a065d3478fb7d2c0970d02207708d8a87d7b61b9cde80ed19617a952a1dba2f70343dfb553b09cfc0e1652ed:922c64590222798bb761d5b6d8e72950