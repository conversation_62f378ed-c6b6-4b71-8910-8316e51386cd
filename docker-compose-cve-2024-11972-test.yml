version: '3.8'

services:
  # WordPress with vulnerable Hunk Companion plugin
  wordpress:
    image: wordpress:6.3-apache
    container_name: wp-hunk-vulnerable
    ports:
      - "8080:80"
    environment:
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress
      WORDPRESS_DB_NAME: wordpress
    volumes:
      - wordpress_data:/var/www/html
      - ./hunk-companion-1.8.0:/var/www/html/wp-content/plugins/hunk-companion
    depends_on:
      - db
    restart: unless-stopped

  # MySQL Database
  db:
    image: mysql:8.0
    container_name: wp-db
    environment:
      MYSQL_DATABASE: wordpress
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress
      MYSQL_ROOT_PASSWORD: rootpassword
    volumes:
      - db_data:/var/lib/mysql
    restart: unless-stopped

  # Nuclei scanner container
  nuclei:
    image: projectdiscovery/nuclei:latest
    container_name: nuclei-scanner
    volumes:
      - ./http/cves/2024:/templates
    command: >
      sh -c "
        echo 'Waiting for WordPress to be ready...' &&
        sleep 30 &&
        echo 'Testing CVE-2024-11972 template...' &&
        nuclei -t /templates/CVE-2024-11972.yaml -u http://wordpress:80 -debug -v
      "
    depends_on:
      - wordpress
    networks:
      - default

volumes:
  wordpress_data:
  db_data:

networks:
  default:
    driver: bridge
