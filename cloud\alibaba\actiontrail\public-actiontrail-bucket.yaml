id: public-actiontrail-bucket

info:
  name: ActionTrail Log Buckets - Publicly Exposed
  author: ritikchaddha
  severity: high
  description: |
    Identify any publicly accessible ActionTrail trail log buckets in order to determine if your Alibaba Cloud account could be at risk. A publicly accessible trail bucket is a bucket were all users, including anonymous users, can perform read and write operations on the objects within the bucket.
  reference:
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-ActionTrail/trail-bucket-publicly-accessible.html
  metadata:
    max-request: 2
    verified: true
  tags: cloud,devops,aliyun,alibaba,aliyun-cloud-config,actiontrail

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for (let BucketName of iterate(template.bucketname)) {
    set("bucket", BucketName)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun actiontrail DescribeTrails --region $region

    extractors:
      - type: json
        name: bucketname
        internal: true
        json:
          - '.TrailList[].OssBucketName'

  - engine:
      - sh
      - bash
    source: |
      ossutil stat oss://$bucket

    matchers:
      - type: regex
        regex:
          - 'ACL.*:.*public-read-write'

    extractors:
      - type: dsl
        dsl:
          - 'bucket + " Bucket associated with ActionTrail trail is publicly accessible"'
# digest: 4b0a004830460221009ad0983bbbc67aceaeefae2e0f2f98390937e979ed51482d79b61c41c11d662602210098b877f52c1cf1cc3273194270ad3d4dbdd9f07b50237fb110adcce97771bb0c:41987585204b393149694b2205534b1a
