id: s3-bucket-key
info:
  name: S3 Bucket Key not enabled
  author: princechaddha
  severity: medium
  description: |
    This template verifies if Amazon S3 buckets have bucket keys enabled to optimize the cost of AWS Key Management Service (SSE-KMS) for server-side encryption
  reference:
    - https://docs.aws.amazon.com/AmazonS3/latest/userguide/manage-versioning-examples.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,s3,aws-cloud-config

flow: |
  code(1)
  for(let bucketName of iterate(template.buckets)){
    set("bucket", bucketName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws s3api list-buckets --query 'Buckets[*].Name'

    extractors:
      - type: json # type of the extractor
        internal: true
        name: buckets
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws s3api get-bucket-encryption --bucket cc-prod-log-bucket --query 'ServerSideEncryptionConfiguration.Rules[?ApplyServerSideEncryptionByDefault.SSEAlgorithm==`aws:kms`].BucketKeyEnabled'

    matchers:
      - type: word
        words:
          - false

    extractors:
      - type: dsl
        dsl:
          - '"Key is not enabled for S3 Bucket " + bucket'
# digest: 4b0a0048304602210083308c84907782cf3348c4fff3389776781332c7deba89a75342b2e764f38c08022100ee305e64ec5617a08240d14705ceaeadaa3bafb59ffc8108c9b668423b5bb25a:922c64590222798bb761d5b6d8e72950