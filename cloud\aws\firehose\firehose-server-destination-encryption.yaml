id: firehose-server-destination-encryption

info:
  name: Firehose Delivery Stream Destination Encryption - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that your Kinesis Firehose delivery stream data records are encrypted at destination (i.e. Amazon S3) in order to meet regulatory requirements and protect your Firehose data at rest.
  impact: |
    Disabling encryption for Firehose delivery stream destinations can lead to sensitive data being stored unencrypted, increasing the risk of data exposure and unauthorized access.
  remediation: |
    Enable encryption for Firehose delivery stream destinations to ensure that all data is encrypted at rest, safeguarding sensitive information from unauthorized access and potential data breaches.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/Firehose/delivery-stream-destination-encryption.html
    - https://docs.aws.amazon.com/AmazonS3/latest/dev/UsingKMSEncryption.html
  tags: cloud,devops,aws,amazon,firehose,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DeliveryStreamNames of iterate(template.deliverys)){
    set("delivery", DeliveryStreamNames)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws firehose list-delivery-streams --region $region --query 'DeliveryStreamNames' --output json

    extractors:
      - type: json
        name: deliverys
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws firehose describe-delivery-stream --region $region --delivery-stream-name $delivery --query 'DeliveryStreamDescription.Destinations[*].ExtendedS3DestinationDescription.EncryptionConfiguration' --output json

    matchers:
      - type: word
        words:
          - "NoEncryption"

    extractors:
      - type: dsl
        dsl:
          - '"Firehose Delivery Stream Destination " + delivery + " Encryption is Disabled"'
# digest: 4a0a00473045022100ffd599cc03999ee32dc74725353120df19ea1200318f380ede42d30cb410ddb6022017ea31b05fa2066146fa33cca3d6e4304f106dd0db9141381e0bb98c017b9c68:922c64590222798bb761d5b6d8e72950