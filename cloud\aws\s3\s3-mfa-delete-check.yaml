id: s3-mfa-delete-check
info:
  name: S3 Bucket MFA Delete Configuration Check
  author: princechaddha
  severity: low
  description: |
    This template verifies that Amazon S3 buckets are configured with Multi-Factor Authentication (MFA) Delete feature, ensuring enhanced protection against unauthorized deletion of versioned objects
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/s3api/get-bucket-versioning.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,s3,aws-cloud-config

flow: |
  code(1)
  for(let bucketName of iterate(template.buckets)){
    set("bucket", bucketName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws s3api list-buckets --query 'Buckets[*].Name'

    extractors:
      - type: json # type of the extractor
        internal: true
        name: buckets
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws s3api get-bucket-versioning --bucket $bucket --query 'MFADelete'

    matchers:
      - type: word
        words:
          - "null"

    extractors:
      - type: dsl
        dsl:
          - '"The S3 bucket " + bucket +" is not configured to use MFA Delete feature"'
# digest: 4a0a00473045022100d5124e78a9716e0b243c2c88d86321a1efcaedb33ac3d4a68932ee2f27f4dd5b02205de1474ee06f1eca0c0eb398856f67d224c3720077f8f460b4a5cfe6b743991d:922c64590222798bb761d5b6d8e72950