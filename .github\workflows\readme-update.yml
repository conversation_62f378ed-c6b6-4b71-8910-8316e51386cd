name: 📝 Readme Update

on:
  workflow_run:
    workflows: ["🗒 Templates Stats"]
    types:
      - completed
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    if: github.repository == 'projectdiscovery/nuclei-templates'
    steps:
      - run: sudo apt-get install tree -y
      - uses: actions/checkout@v5
      - uses: actions/setup-python@v5
        with:
          python-version: "3.8"
      - run: python .github/scripts/update-readme.py
      - uses: projectdiscovery/actions/setup/git@v1
      - uses: projectdiscovery/actions/commit@v1
        with:
          files: 'README.md README_*.md'
          message: 'docs: update README 🤖'
      - run: git push origin $GITHUB_REF
