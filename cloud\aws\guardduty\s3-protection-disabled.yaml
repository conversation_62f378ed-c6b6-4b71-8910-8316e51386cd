id: s3-protection-disabled

info:
  name: GuardDuty S3 Protection - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that the S3 Protection feature is enabled for your Amazon GuardDuty detectors.
  impact: |
    GuardDuty S3 Protection disabled increases the risk of undetected malicious activities, such as unauthorized access or data exfiltration, within S3 buckets.
  remediation: |
    Enable GuardDuty S3 Protection by configuring it in the GuardDuty console or via AWS CLI to monitor S3 buckets for unauthorized access and malicious activities.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/GuardDuty/enable-s3-protection.html
    - https://docs.aws.amazon.com/guardduty/latest/ug/s3-protection.html
  tags: cloud,devops,aws,amazon,guardduty,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DetectorIds of iterate(template.detectors)){
    set("detector", DetectorIds)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws guardduty list-detectors --region $region --query 'DetectorIds' --output json

    extractors:
      - type: json
        name: detectors
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws guardduty get-detector --region $region --detector-id "$detector" --query 'DataSources.S3Logs.Status' --output json

    matchers:
      - type: word
        words:
          - "DISABLED"

    extractors:
      - type: dsl
        dsl:
          - '"GuardDuty S3 Protection " + detector + " is Disabled"'
# digest: 490a004630440220387e0c5119e515ad2a417b55a14385fa7351bff2ebaa4573cbf2b5e8c477565402201cb8e0b93464b42fe2a406ab5f4482bd873a0315161f070c00f8cefd70f86b5e:922c64590222798bb761d5b6d8e72950