id: secrets-rotation-disabled

info:
  name: Secret Rotation Disabled
  author: <PERSON><PERSON>yaneshDK
  severity: medium
  description: |
    Ensure that AWS Secrets Manager service is configured to automatically rotate your service or database secrets (i.e. enable automatic rotation feature for your secrets).
  impact: |
    Secret rotation disabled in AWS increases the risk of credential compromise and prolonged unauthorized access due to outdated or exposed secrets.
  remediation: |
    Enable automatic secret rotation in AWS Secrets Manager by configuring a rotation schedule and associating a Lambda function to periodically update and securely rotate the secrets.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/SecretsManager/rotation-enabled.html
    - https://docs.aws.amazon.com/cli/latest/reference/secretsmanager/rotate-secret.html
  tags: cloud,devops,aws,amazon,secrets-manager,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let SecretListName of iterate(template.secrets)){
    set("secretlist", SecretListName)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws secretsmanager list-secrets --region $region --query 'SecretList[*].Name' --output json

    extractors:
      - type: json
        name: secrets
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws secretsmanager describe-secret --region $region --secret-id $secretlist --query 'RotationEnabled'

    matchers:
      - type: word
        words:
          - "false"

    extractors:
      - type: dsl
        dsl:
          - '"Secrets Rotation  " + secretlist + " is disabled"'
# digest: 4a0a0047304502210091fb35bdf580e7dbd598d4cb8e8bc9de92998ab1a3030383f2d9bbb9efa7ef2c022028654469d379fa555d4e2240db82575a5372d4c7504e03e12709889a655876ab:922c64590222798bb761d5b6d8e72950