id: iam-unapproved-policy
info:
  name: Unapproved IAM Policy Attachments
  author: princechaddha
  severity: high
  description: |
    Checks for the attachment of unapproved Amazon IAM managed policies to IAM roles, users, or groups, ensuring compliance with organizational access policies
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/iam/get-policy.html
  tags: cloud,devops,aws,amazon,iam,ssl,tls,aws-cloud-config

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws iam get-policy  --policy-arn arn:aws:iam::aws:policy/AmazonRDSFullAccess  --query 'Policy.{"AttachmentCount": AttachmentCount}'

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "AttachmentCount"

      - type: word
        part: body
        words:
          - '"AttachmentCount": 0'
        negative: true

    extractors:

      - type: dsl
        dsl:
          - '"Unapproved IAM policy is used within your AWS cloud account"'
# digest: 4b0a00483046022100cdd6b0920f4ce3c606a33251020083ad789e1ae54aa7687c8396b8c37d8f237a022100ecfd880380fd88e62ec2dcd015543d9be876e70e84894d446b028405fd1c167b:922c64590222798bb761d5b6d8e72950