id: iam-root-mfa
info:
  name: MFA not enabled on AWS Root Account
  author: princechaddha
  severity: high
  description: |
    Checks if Multi-Factor Authentication (MFA) is enabled for the AWS root account
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/iam/get-account-summary.html
  tags: cloud,devops,aws,amazon,iam,aws-cloud-config

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws iam get-account-summary | jq -r '.SummaryMap.AccountMFAEnabled'

    matchers:
      - type: word
        words:
          - "0"

    extractors:
      - type: dsl
        dsl:
          - '"MFA is not enabled on your AWS Root account"'
# digest: 4a0a00473045022100d201d32b0da13fa8e03a3a9fef2626d353aa472e1372bd17988dda8bb9ed6c9a022043264b286449598c5863fcf4ebf279221f5e85c142e61fb684bb7b0c6f473565:922c64590222798bb761d5b6d8e72950