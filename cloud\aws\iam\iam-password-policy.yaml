id: iam-password-policy
info:
  name: IAM Password Policy Not Configured
  author: princechaddha
  severity: medium
  description: |
    Verifies that Amazon IAM users adhere to a strong password policy, including requirements for minimum length, expiration, and pattern
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/iam/get-account-password-policy.html
  tags: cloud,devops,aws,amazon,iam,aws-cloud-config

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws iam get-account-password-policy

    matchers:
      - type: word
        words:
          - "NoSuchEntity"

    extractors:
      - type: dsl
        dsl:
          - '"AWS cloud account is not configured with a custom IAM password policy"'
# digest: 4b0a004830460221008b9f96cd3ec0ecb7f1de8e6c08a6d0161aa0631f10a620e04f2d84028b4e3dcf022100d4cb25db3be1e191a13eadf958d3efc48ab53a3dd983c01c52284c371275d065:922c64590222798bb761d5b6d8e72950