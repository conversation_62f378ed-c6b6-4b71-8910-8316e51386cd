id: route53-dnssec-signing-disabled

info:
  name: DNSSEC Signing for Route 53 Hosted Zones - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that Domain Name System Security Extensions (DNSSEC) signing is enabled for your Amazon Route 53 public hosted zones in order to protect your domains against spoofing and cache poisoning attacks. By default, DNSSEC signing is not enabled for Route 53 hosted zones.
  impact: |
    Attackers can hijack the process of domain/IP lookup and redirect users to malicious web content through DNS hijacking and Man-In-The-Middle (MITM) attacks. DNSSEC security feature helps mitigate the risk of such attacks by encrypting signing DNS records.
  remediation: |
    Enable DNSSEC signing in the Route 53 console for the hosted zone, sign the zone with a strong key algorithm, and ensure all DNS records are published correctly.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/Route53/enable-query-logging.html
    - https://docs.aws.amazon.com/Route53/latest/DeveloperGuide/monitoring-overview.html
  tags: cloud,devops,aws,amazon,route53,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let HostedZones of iterate(template.hostedzones)){
    set("hostedzone", HostedZones)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws route53 list-hosted-zones --region $region --query "HostedZones[*].Id" --output json

    extractors:
      - type: json
        name: hostedzones
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws route53 get-dnssec --region $region --hosted-zone-id "$hostedzone" --query "Status.ServeSignature" --output json

    matchers:
      - type: word
        words:
          - 'NOT_SIGNING'

    extractors:
      - type: dsl
        dsl:
          - 'hostedzone + " DNSSEC Signing for Route 53 Hosted Zones is Disabled"'
# digest: 4a0a004730450220045aab745aaa0e4f28c572d21afb2982e084a0f6a58236fe422e6e2aa7795b2c022100ba81371510b3f4573f1e87ca23ba3c842ccc68db6a519296132e8d4e1330fa9f:922c64590222798bb761d5b6d8e72950