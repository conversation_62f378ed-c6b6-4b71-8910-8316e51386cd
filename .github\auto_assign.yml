# Set to true to add reviewers to pull requests
addReviewers: false

# Set to true to add assignees to pull requests
addAssignees: false

# A list of reviewers to be added to pull requests (GitHub user name)
reviewers:
  - ritikchaddha
  - pussycat0x
  - DhiyaneshGeek

# A number of reviewers added to the pull request
# Set 0 to add all the reviewers (default: 0)
numberOfReviewers: 1

 # A list of assignees, overrides reviewers if set
assignees:
  - pussycat0x
  - ritikchaddha
  - DhiyaneshGeek

# A number of assignees to add to the pull request
# Set to 0 to add all of the assignees.
# Uses numberOfReviewers if unset.
numberOfAssignees: 1

# A list of keywords to be skipped the process that add reviewers if pull requests include it
# skipKeywords:
#   - wip
