id: password-policy-symbol-unconfigured

info:
  name: RAM Password Policy requires atleast One Symbol - Unconfigured
  author: DhiyaneshDK
  severity: medium
  description: |
    The Alibaba Cloud RAM password policy is unconfigured to require at least one symbol, leaving user accounts vulnerable to weak passwords. Enforcing this requirement is essential to ensure stronger, more secure passwords and reduce the risk of unauthorized access.
  reference:
    - https://www.alibabacloud.com/help/en/ram/user-guide/configure-a-password-policy-for-ram-users
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RAM/require-symbol-password-policy.html
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-ram

variables:
  region: "cn-hangzhou"

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun ram GetPasswordPolicy --region $region

    matchers:
      - type: word
        name: policy
        words:
          - '"RequireSymbols": false'

    extractors:
      - type: dsl
        dsl:
          - '"RAM Password Policy Does not contain One Symbol "'
# digest: 4b0a00483046022100fbcf64ed10963ef6c5ab722e556f906d1dc1de40dbe530d92050f8204af6e9b6022100d2c96ba77e32dbe2bb0467363d98de73b94dc328b9adae761a51a3b507e59334:922c64590222798bb761d5b6d8e72950