id: rds-snapshot-encryption
info:
  name: RDS Snapshot Encryption
  author: princechaddha
  severity: medium
  description: |
    Ensure Amazon RDS database snapshots are encrypted for data-at-rest compliance within AWS environments.
  impact: |
    Unencrypted RDS snapshots can expose sensitive data to unauthorized access, risking data breach and non-compliance penalties.
  remediation: |
    Enable encryption for RDS snapshots by using AWS KMS Customer Master Keys (CMKs) for enhanced data security and compliance.
  reference:
    - https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_EncryptSnapshot.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config
variables:
  region: "ap-northeast-1"

flow: |
  code(1)
  for(let RDPsnaps of iterate(template.snapshots)){
    set("snapshot", RDPsnaps)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-snapshots --region $region --snapshot-type manual --output json --query 'DBSnapshots[*].DBSnapshotIdentifier'

    extractors:
      - type: json
        name: snapshots
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws rds describe-db-snapshots --region $region --db-snapshot-identifier $snapshot --query 'DBSnapshots[*].Encrypted'

    matchers:
      - type: word
        words:
          - 'false'

    extractors:
      - type: dsl
        dsl:
          - '"Amazon RDS database snapshot " + snapshot + " is not encrypted"'
# digest: 4a0a0047304502201d95938e7527cf027ea27405441db817a139bd5f7ae6273c2d0285b8df4436a90221009af5fede920003116fbbb1f20eee1c7b1a14f5b7fd343a5b5f72ff6c9f7675eb:922c64590222798bb761d5b6d8e72950