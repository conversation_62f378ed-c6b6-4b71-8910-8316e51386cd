name: 🤖 issue/pr assignment
on:
#  pull_request:
#    types: [opened]
#    branches: 
#      - main
  issues:
    types: [opened]

env:
  ASSIGN_TASK_TOKEN: ${{ secrets.PDTEAMX_PAT }} # github personal token

jobs:
  build:
    permissions: write-all
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5 # checkout the repository content
      - uses: actions/setup-python@v5
        with:
          python-version: '3.10' # install the python version needed
      - run: pip install requests
#      - if: github.event_name == 'pull_request'
#        run: python .github/scripts/assign_tasks.py ${{ github.event.pull_request.number }} pr ${{ secrets.GITHUB_TOKEN }}
      - if: github.event_name == 'issues'
        run: python .github/scripts/assign_tasks.py ${{ github.event.issue.number }} issue ${{ secrets.GITHUB_TOKEN }}
