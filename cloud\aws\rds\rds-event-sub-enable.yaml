id: rds-event-sub-enable
info:
  name: RDS Event Subscription Not Enabled
  author: princechaddha
  severity: high
  description: |
    Ensures Amazon RDS event notifications are enabled for database instance level events, allowing for real-time alerts on operational changes.
  impact: |
    Lack of event notifications may delay the response to critical RDS operational events, affecting database availability and performance.
  remediation: |
    Enable RDS event notification subscriptions for relevant database instance level events through the AWS Management Console or AWS CLI.
  reference:
    - https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Events.html
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config

variables:
  region: "ap-northeast-1"

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-event-subscriptions --region $region --query "EventSubscriptionsList[?SourceType == 'db-instance'].CustSubscriptionId"

    matchers:
      - type: word
        words:
          - '[]'

    extractors:
      - type: dsl
        dsl:
          - '"There are no Amazon RDS event subscriptions created for instance level events in " + region + " AWS region"'
# digest: 4b0a00483046022100c570b20803b135e2f2c84679a1ce3f4afdebdbabf27e1788effe477f81776e76022100f64c818db336c2f0c33e20601c43e315aaa6714a6922f524bfb5be36dd038d6a:922c64590222798bb761d5b6d8e72950