version: '3.8'

services:
  # Advanced PoC Container
  advanced-poc:
    build:
      context: .
      dockerfile: poc/Dockerfile
    container_name: cve-2025-30154-advanced-poc
    volumes:
      - ./results:/app/results
      - ./tools:/app/tools
      - ./samples:/app/samples
    environment:
      - POC_MODE=advanced
      - ATTACK_SIMULATION=true
      - GENERATE_DEBUG_DATA=true
      - TIMESTAMP=2025-03-11T19:15:42Z
    command: |
      bash -c "
        echo '🚀 Starting CVE-2025-30154 Advanced PoC Suite'
        echo '================================================'
        
        # Run advanced simulation
        ./poc/advanced-simulation.sh
        
        # Run log analysis
        echo '🔍 Running advanced log analysis...'
        python3 tools/advanced-logscan.py -i results/malicious-payload.txt -o results/analysis-results.json -v
        
        # Generate comprehensive report
        echo '📊 Generating comprehensive report...'
        cat > results/docker-execution-report.md << 'EOF'
        # Docker Execution Report
        
        ## Execution Details
        - Container: cve-2025-30154-advanced-poc
        - Timestamp: $(date -u)
        - Mode: Advanced PoC Suite
        
        ## Generated Artifacts
        - Malicious payload simulation
        - Advanced log analysis results
        - Supply chain context analysis
        - Historical compromise indicators
        
        ## Validation
        All components executed successfully in isolated Docker environment.
        EOF
        
        echo '✅ Advanced PoC Suite completed successfully!'
        echo '📁 Results available in ./results/ directory'
        
        # Keep container running for inspection
        tail -f /dev/null
      "
    networks:
      - poc-network

  # Nuclei Testing Container
  nuclei-tester:
    image: projectdiscovery/nuclei:latest
    container_name: cve-2025-30154-nuclei-tester
    volumes:
      - .:/workspace
      - ./results:/results
    working_dir: /workspace
    depends_on:
      - test-server
    command: |
      bash -c "
        echo '🎯 Starting Nuclei Template Testing'
        echo '=================================='
        
        # Wait for test server
        sleep 5
        
        # Test HTTP template
        echo '🌐 Testing HTTP template...'
        nuclei -t /workspace/../../http/cves/2025/CVE-2025-30154.yaml -u http://test-server:8080 -debug -v
        
        # Test file template
        echo '📁 Testing file template...'
        nuclei -t /workspace/../../file/malware/cve-2025-30154-supply-chain.yaml -target /workspace/samples/ -debug -v
        
        echo '✅ Nuclei testing completed!'
        
        # Keep container running
        tail -f /dev/null
      "
    networks:
      - poc-network

  # Test HTTP Server
  test-server:
    image: python:3.11-slim
    container_name: cve-2025-30154-test-server
    volumes:
      - ./samples:/app/samples
    working_dir: /app
    command: |
      bash -c "
        echo '🌐 Starting test HTTP server'
        
        # Create vulnerable workflow files
        mkdir -p .github/workflows
        cat > .github/workflows/vulnerable.yml << 'EOF'
        name: Vulnerable Workflow (Test)
        on: [push, pull_request]
        jobs:
          test:
            runs-on: ubuntu-latest
            steps:
              - name: Checkout
                uses: actions/checkout@v4
              - name: Setup reviewdog (VULNERABLE)
                uses: reviewdog/action-setup@v1
                with:
                  reviewdog_version: latest
              - name: Malicious step simulation
                run: |
                  echo '🐶 Preparing environment...'
                  echo 'VGVzdCBzZWNyZXQ6IGdoc19mYWtlX3Rva2VuXzEyMzQ1Njc4OTA='
              - name: Supply chain indicator
                uses: tj-actions/changed-files@v1
        EOF
        
        # Create additional test files
        cat > .github/workflows/safe.yml << 'EOF'
        name: Safe Workflow (Control)
        on: [push]
        jobs:
          test:
            runs-on: ubuntu-latest
            steps:
              - uses: actions/checkout@v4
              - run: echo 'This is a safe workflow'
        EOF
        
        # Start HTTP server
        python3 -m http.server 8080
      "
    ports:
      - "8080:8080"
    networks:
      - poc-network

  # Log Analysis Service
  log-analyzer:
    build:
      context: .
      dockerfile: tools/Dockerfile
    container_name: cve-2025-30154-log-analyzer
    volumes:
      - ./results:/app/results
      - ./samples:/app/samples
      - ./tools:/app/tools
    environment:
      - ANALYSIS_MODE=comprehensive
      - OUTPUT_FORMAT=json
    command: |
      bash -c "
        echo '🔍 Starting Advanced Log Analysis Service'
        echo '========================================'
        
        # Wait for PoC to generate data
        sleep 10
        
        # Run comprehensive analysis
        echo 'Running comprehensive log analysis...'
        python3 tools/advanced-logscan.py -i results/malicious-payload.txt -o results/comprehensive-analysis.json -v
        
        # Analyze historical data
        echo 'Analyzing historical compromise indicators...'
        python3 tools/advanced-logscan.py -i results/historical-workflow-logs.txt -o results/historical-analysis.json -v
        
        # Generate summary report
        cat > results/log-analysis-summary.md << 'EOF'
        # Log Analysis Summary
        
        ## Analysis Results
        - Comprehensive analysis completed
        - Historical indicators detected
        - Supply chain context analyzed
        - Remediation recommendations generated
        
        ## Key Findings
        - Double base64 encoded payloads detected
        - Attack timeline indicators found
        - Supply chain compromise patterns identified
        
        ## Validation
        All analysis tools executed successfully in Docker environment.
        EOF
        
        echo '✅ Log analysis completed!'
        
        # Keep running for inspection
        tail -f /dev/null
      "
    networks:
      - poc-network

networks:
  poc-network:
    driver: bridge

volumes:
  poc-results:
    driver: local
