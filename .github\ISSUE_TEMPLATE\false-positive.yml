name: False Positive
description: Report templates with false positive results.
title: "[FALSE-POSITIVE] ..."
labels: ["false-positive"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this false-positive report!

        :warning: **Issues missing important information may be closed without further investigation.**
  - type: textarea
    attributes:
      label: Template IDs or paths
      description: |
        Examples:
          - CVE-202A-YYYYY
          - CVE-202B-YYYYY
          - http/cves/CVE-202C-YYYYY.yaml
      value: |
          - ...
      render: markdown
    validations:
      required: true
  - type: textarea
    attributes:
      label: Environment
      description: |
        Examples:
          - **OS**: Ubuntu 20.04
          - **Nuclei** (`nuclei -version`): v3.3.3
          - **Go** (`go version`): go1.22.0 _(only if you've installed it via `go install` command)_
      value: |
          - OS: 
          - Nuclei: 
          - Go: 
      render: markdown
    validations:
      required: false
  - type: textarea
    attributes:
      label: Steps To Reproduce
      description: |
        Steps to reproduce the behavior, for example, commands to run the templates with Nuclei.

        :warning: **Please redact any literal target hosts/URLs or other sensitive information.**
      placeholder: |
        1. Run `nuclei -t ...`
    validations:
      required: true
  - type: textarea
    attributes:
      label: Relevant dumped responses
      description: |
        Please copy and paste any relevant dumped responses (`-dresp`/`-debug-resp`). This will be automatically formatted into code, so no need for backticks.

        :warning: **Please redact any literal target hosts/URLs or other sensitive information.**
      render: shell
  - type: textarea
    attributes:
      label: Anything else?
      description: |
        Links? References? Trace (`-tlog`/`-trace-log`) or error (`-elog`/`-error-log`) log? Anything that will give us more context about the issue you are encountering!

        Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
    validations:
      required: false
