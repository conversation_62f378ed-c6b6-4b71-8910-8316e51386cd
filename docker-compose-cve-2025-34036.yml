version: '3.8'

services:
  vulnerable-dvr:
    build:
      context: ./vulnerable-dvr-sim
      dockerfile: Dockerfile
    container_name: tvt-dvr-vulnerable
    restart: always
    ports:
      - "8081:81"
      - "8082:82"
      - "8000:8000"
    environment:
      - DVR_MODEL=TVT-TD2316TS-HC
      - FIRMWARE_VERSION=V4.02.R11.00000000.10001.131201
      - CROSS_WEB_SERVER=enabled
    volumes:
      - dvr_data:/mnt/mtd
      - ./dvr-logs:/var/log/dvr
    networks:
      - dvr_network

  nuclei-scanner:
    image: projectdiscovery/nuclei:latest
    container_name: nuclei-cve-2025-34036
    volumes:
      - ./http/cves/2025/CVE-2025-34036.yaml:/templates/CVE-2025-34036.yaml
      - ./scan-results:/results
    command: >
      sh -c "
        echo 'Waiting for DVR to be ready...' &&
        sleep 30 &&
        nuclei -t /templates/CVE-2025-34036.yaml -u http://vulnerable-dvr:81 -debug -o /results/scan-results.txt &&
        nuclei -t /templates/CVE-2025-34036.yaml -u http://vulnerable-dvr:82 -debug -o /results/scan-results-82.txt &&
        nuclei -t /templates/CVE-2025-34036.yaml -u http://vulnerable-dvr:8000 -debug -o /results/scan-results-8000.txt
      "
    depends_on:
      - vulnerable-dvr
    networks:
      - dvr_network

  python-scanner:
    build:
      context: ./python-scanner
      dockerfile: Dockerfile
    container_name: python-cve-2025-34036
    volumes:
      - ./scan-results:/results
      - ./cve-2025-34036-scanner.py:/scanner/scanner.py
    command: >
      sh -c "
        echo 'Starting Python scanner...' &&
        sleep 35 &&
        python3 /scanner/scanner.py -u http://vulnerable-dvr:81 -v > /results/python-scan-81.txt &&
        python3 /scanner/scanner.py -u http://vulnerable-dvr:82 -v > /results/python-scan-82.txt &&
        python3 /scanner/scanner.py -u http://vulnerable-dvr:8000 -v > /results/python-scan-8000.txt
      "
    depends_on:
      - vulnerable-dvr
    networks:
      - dvr_network

volumes:
  dvr_data:

networks:
  dvr_network:
    driver: bridge
