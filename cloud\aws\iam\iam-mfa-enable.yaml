id: iam-mfa-enable
info:
  name: MFA not enabled for AWS IAM Console User
  author: princechaddha
  severity: high
  description: |
    Verifies that Multi-Factor Authentication (MFA) is enabled for all IAM users with console access in AWS
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/iam/list-mfa-devices.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,iam,aws-cloud-config

flow: |
  code(1)
  for(let UserName of iterate(template.users)){
    set("user", UserName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws iam list-users --query 'Users[*].UserName'

    extractors:
      - type: json # type of the extractor
        internal: true
        name: users
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws iam list-mfa-devices --user-name $user --query 'MFADevices'

    matchers:
      - type: word
        words:
          - "[]"

    extractors:
      - type: dsl
        dsl:
          - '"MFA is no enabled for IAM User " + user'
# digest: 480a00453043021f73b26c8684f06e39fd82a9a9b41914ff5ba899fdab4b9c1f73d29e3a2bedb002205ee954e980c1f8ffe33918fa4fa1b1e49426260bdaecf2e4d368e5b36bfcb4ce:922c64590222798bb761d5b6d8e72950