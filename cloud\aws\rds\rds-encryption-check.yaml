id: rds-encryption-check
info:
  name: RDS Instance Encryption
  author: princechaddha
  severity: high
  description: |
    Ensure that your Amazon RDS database instances are encrypted to fulfill compliance requirements for data-at-rest encryption.
  impact: |
    Non-encrypted RDS instances may lead to data breaches, failing to comply with data protection regulations, which could result in hefty fines and loss of reputation.
  remediation: |
    Enable encryption for your Amazon RDS instances by modifying the instance and setting the "Storage Encrypted" option to true. For new instances, enable encryption within the launch wizard.
  reference:
    - https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Overview.Encryption.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config
variables:
  region: "ap-northeast-1"

flow: |
  code(1)
  for(let DBInstances of iterate(template.instances)){
    set("db", DBInstances)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --output json --query 'DBInstances[*].DBInstanceIdentifier'

    extractors:
      - type: json
        name: instances
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws rds describe-db-instances --region $region --db-instance-identifier $db --query 'DBInstances[*].{"StorageEncrypted":StorageEncrypted,"KmsKeyId":KmsKeyId}'

    matchers:
      - type: word
        words:
          - 'false'

    extractors:
      - type: dsl
        dsl:
          - '"The encryption of data at rest is not enabled for " + db + " RDS database instance"'
# digest: 4b0a00483046022100fbf2edd268c9635bbd530350e8ebc73f889d847a62d987267024eec5d0271e8e0221009966d74dfc170d097cb4a123d92ff610254c776016ec8f940c08c007c56d7080:922c64590222798bb761d5b6d8e72950