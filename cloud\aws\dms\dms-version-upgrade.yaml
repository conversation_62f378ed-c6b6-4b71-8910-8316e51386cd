id: dms-version-upgrade

info:
  name: DMS Auto Minor Version Upgrade
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that your Amazon Database Migration Service (DMS) replication instances have the Auto Minor Version Upgrade feature enabled in order to receive automatically minor engine upgrades.
  impact: |
    Not enabling DMS Auto Minor Version Upgrade can lead to running outdated database versions, increasing vulnerability to security risks and bugs, while missing out on performance improvements and new features provided in minor updates.
  remediation: |
    Enable DMS Auto Minor Version Upgrade to automatically apply minor version updates, ensuring your database is always up-to-date with the latest security patches, performance enhancements, and bug fixes.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/DMS/auto-minor-version-upgrade.html
    - https://docs.aws.amazon.com/cli/latest/reference/dms/index.html
  tags: cloud,devops,aws,amazon,dms,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let ReplicationInstances of iterate(template.replications)){
    set("replication", ReplicationInstances)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws dms describe-replication-instances --region $region --query "ReplicationInstances[*].ReplicationInstanceArn" --output json

    extractors:
      - type: json
        name: replications
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws dms describe-replication-instances --region $region --filters Name=replication-instance-arn,Values=$replication --query "ReplicationInstances[*].AutoMinorVersionUpgrade" --output json

    matchers:
      - type: word
        words:
          - "false"

    extractors:
      - type: dsl
        dsl:
          - '"DMS Auto Minor Version Upgrade  " + replication + " not enabled"'
# digest: 4b0a00483046022100dd9841e17fd251664e804b43a5ec2e373b4ad37c3321cc3cd980f99ba6078953022100ad38fe36697b3715fd2e059494e8ebc86cc4161dd952eba8a2096a4d3943687d:922c64590222798bb761d5b6d8e72950