id: s3-access-logging
info:
  name: S3 Bucket - Access Logging Not Enabled
  author: princechaddha
  severity: medium
  description: |
    This template verifies if the Server Access Logging feature is enabled for Amazon S3 buckets, which is essential for tracking access requests for security and audit purposes.
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/s3api/get-bucket-encryption.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,s3,aws-cloud-config

flow: |
  code(1)
  for(let bucketName of iterate(template.buckets)){
    set("bucket", bucketName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws s3api list-buckets --query 'Buckets[*].Name'

    extractors:
      - type: json # type of the extractor
        internal: true
        name: buckets
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws s3api get-bucket-logging --bucket $bucket --query 'LoggingEnabled'

    matchers:
      - type: word
        words:
          - "null"

    extractors:
      - type: dsl
        dsl:
          - '"The S3 bucket " + bucket +" does not have access logging enabled."'
# digest: 4a0a0047304502203c75e4fb81380c936939151cea8ad87f26593a71bd98ec550e4dd7a9dea8273b02210093c2c7969387aebf8e4559969f628fcec34ead50f7d705f5532b9db84382991f:922c64590222798bb761d5b6d8e72950