id: cloudtrail-integrated-cloudwatch
info:
  name: CloudTrail CloudWatch Integration
  author: princechaddha
  severity: medium
  description: |
    Ensure Amazon CloudTrail logs are integrated with CloudWatch Logs for real-time monitoring and analysis.
  impact: |
    Without integration, detecting and responding to critical events or unauthorized actions within AWS environment could be delayed.
  remediation: |
    Enable CloudTrail log file validation and configure CloudWatch Logs to monitor CloudTrail log files. Create CloudWatch Alarms for specific events of interest.
  reference:
    - https://docs.aws.amazon.com/awscloudtrail/latest/userguide/send-cloudtrail-events-to-cloudwatch-logs.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,cloudtrail,cloudwatch,aws-cloud-config
variables:
  region: "ap-south-1"

flow: |
  code(1)
  for(let CloudTrail of iterate(template.cloudtrailname)){
    set("trail", CloudTrail)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws cloudtrail list-trails --region $region --query 'Trails[*].Name' --output json

    extractors:
      - type: json
        name: cloudtrailname
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws cloudtrail describe-trails --region $region --trail-name-list $trail --query 'trailList[*].CloudWatchLogsLogGroupArn'

    matchers:
      - type: word
        words:
          - "[]"

    extractors:
      - type: dsl
        dsl:
          - '"CloudTrail trail" + trail + " is not configured to send events to CloudWatch Logs for monitoring purposes"'
# digest: 4a0a00473045022079c294096ed170716849b5fb7d05822bd381caa9fcfef87b21ce761be412ea43022100b59348b2f6da4316675f81e0640c8c0c5530730d6dca6d63ea603a0cad3323c3:922c64590222798bb761d5b6d8e72950