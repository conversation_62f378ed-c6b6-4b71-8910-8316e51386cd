id: s3-public-read

info:
  name: S3 Bucket with Public READ Access
  author: princechaddha
  severity: critical
  description: |
    Verifies that Amazon S3 buckets do not permit public 'READ' (LIST) access to anonymous users, protecting against unauthorized data exposure
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/s3api/get-bucket-acl.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,s3,aws-cloud-config

flow: |
  code(1)
  for(let bucketName of iterate(template.buckets)){
    set("bucket", bucketName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws s3api list-buckets --query 'Buckets[*].Name'

    extractors:
      - type: json # type of the extractor
        internal: true
        name: buckets
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws s3api get-bucket-acl --bucket $bucket --query 'Grants[?(Grantee.URI==`http://acs.amazonaws.com/groups/global/AllUsers`)]'

    matchers:
      - type: word
        words:
          - '"Permission": "READ"'

    extractors:
      - type: dsl
        dsl:
          - '"The S3 bucket " + bucket +" have public READ access"'
# digest: 490a0046304402200714d057b96220457f882153c1fe101f98cac27af607c2dfd9dfc80d1135272002200c7be5cf0aed1629c3f0290a49039c5095f709f97a10971e7e34b9c8662b06ed:922c64590222798bb761d5b6d8e72950