id: cloudfront-insecure-protocol

info:
  name: CloudFront Insecure Origin SSL Protocols
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that your Amazon CloudFront Content Delivery Network (CDN) distributions are not using insecure SSL protocols (i.e. SSLv3) for HTTPS communication between CloudFront edge locations and custom origins.
  impact: |
    Insecure SSL protocols for CloudFront origins can expose sensitive data to interception and compromise, increasing the risk of man-in-the-middle attacks.
  remediation: |
    Configure your CloudFront distribution to enforce the use of secure SSL/TLS protocols (TLS 1.2 or higher) for all origins and disable support for outdated protocols like SSLv3 and TLS 1.0/1.1.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/CloudFront/cloudfront-insecure-origin-ssl-protocols.html
    - http://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/SecureConnections.html
  tags: cloud,devops,aws,amazon,cloudfront,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DistributionListItemsId of iterate(template.distributions)){
    set("distribution", DistributionListItemsId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash

    source: |
      aws cloudfront list-distributions --output table --query 'DistributionList.Items[*].Id' --region $region --output json

    extractors:
      - type: json
        name: distributions
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws cloudfront get-distribution --id $distribution --query 'Distribution.DistributionConfig.Origins.Items[*].CustomOriginConfig.OriginSslProtocols.Items | []' --region $region --output json

    matchers:
      - type: word
        words:
          - "SSLv3"

    extractors:
      - type: dsl
        dsl:
          - '"CloudFront Uses SSLv3 Protocol in" + distribution'
# digest: 490a0046304402203f82a403b2d5299d4e40fe21ec8d29df8dca63b594e26c055fb1aea9e3b183e9022070d55cd893d231f70803f0755dfbf169c2917ea01d600a785c3b5e5e4277a9ae:922c64590222798bb761d5b6d8e72950