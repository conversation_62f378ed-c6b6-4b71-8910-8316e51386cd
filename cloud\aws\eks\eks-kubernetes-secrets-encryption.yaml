id: eks-kubernetes-secrets-encryption

info:
  name: EKS Kubernetes Secrets not Encrypted
  author: princechaddha
  severity: high
  description: |
    Ensure that your Amazon Elastic Kubernetes Service (EKS) clusters have encryption enabled for Kubernetes secrets using AWS KMS Customer Master Keys (CMKs). This is a security best practice for protecting sensitive data stored in Kubernetes secrets.
  impact: |
    Without encryption enabled, Kubernetes secrets are stored in plaintext, making them vulnerable to unauthorized access and potentially violating compliance requirements for data-at-rest encryption.
  remediation: |
    Enable encryption for your EKS cluster by creating a KMS CMK and configuring it for the cluster. Note that this requires recreating the cluster with the encryption configuration.
  reference:
    - https://docs.aws.amazon.com/eks/latest/userguide/encryption-configuration.html
    - https://docs.aws.amazon.com/eks/latest/userguide/update-cluster.html
  tags: cloud,devops,aws,amazon,eks,aws-cloud-config

variables:
  region: "us-east-1"

flow: |
  code(1)
  for(let cluster of iterate(template.clusters)){
    set("cluster", cluster)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws eks list-clusters --region $region --query 'clusters' --output json

    extractors:
      - type: json
        name: clusters
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
      aws eks describe-cluster --region $region --name $cluster --query 'cluster.encryptionConfig[*].provider.keyArn' --output json

    matchers:
      - type: word
        words:
          - "null"

    extractors:
      - type: dsl
        dsl:
          - '"EKS cluster " + cluster + " does not have encryption enabled for Kubernetes secrets"'
# digest: 4b0a0048304602210095384f513dd7c3cfcf985e2bb4c95fd1ef48deb4c6d60d5e2068f5be999999a7022100dc5163ca0d48f98dd9991cfb47bfa72632a09376f17fb74868870b8816f9e944:922c64590222798bb761d5b6d8e72950