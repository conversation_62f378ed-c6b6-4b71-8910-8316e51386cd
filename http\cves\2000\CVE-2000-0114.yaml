id: CVE-2000-0114

info:
  name: Microsoft FrontPage Extensions Check (shtml.dll)
  author: r3naissance
  severity: medium
  description: Frontpage Server Extensions allows remote attackers to determine the name of the anonymous account via an RPC POST request to shtml.dll in the /_vti_bin/ virtual directory.
  impact: |
    High: Remote code execution or denial of service.
  remediation: Upgrade to the latest version.
  reference:
    - https://nvd.nist.gov/vuln/detail/CVE-2000-0114
    - https://www.exploit-db.com/exploits/19897
    - https://exchange.xforce.ibmcloud.com/vulnerabilities/CVE-2000-0114
    - https://github.com/0xPugazh/One-Liners
    - https://github.com/ARPSyndicate/kenzer-templates
  classification:
    cvss-metrics: CVSS:2.0/AV:N/AC:L/Au:N/C:P/I:N/A:N
    cvss-score: 5
    cve-id: CVE-2000-0114
    cwe-id: NVD-CWE-Other
    epss-score: 0.02967
    epss-percentile: 0.85545
    cpe: cpe:2.3:a:microsoft:internet_information_server:3.0:*:*:*:*:*:*:*
  metadata:
    max-request: 1
    vendor: microsoft
    product: internet_information_server
    shodan-query: cpe:"cpe:2.3:a:microsoft:internet_information_server"
  tags: cve,cve2000,frontpage,microsoft,edb

http:
  - method: GET
    path:
      - '{{BaseURL}}/_vti_inf.html'

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "_vti_bin/shtml.dll"

      - type: status
        status:
          - 200
# digest: 490a0046304402205f1e2e9658b4b5031f4e0de671b50bb742ac3a33a72373771a436a06aa34dd7b02204f75b2cdd3eaac2623232db2cfab38cc344644a6f56dc7dc76869e055b0d93cc:922c64590222798bb761d5b6d8e72950