### Template / PR Information

This PR adds a new Nuclei template for CVE-2025-6543, a critical buffer overflow vulnerability in Citrix NetScaler ADC and NetScaler Gateway affecting Gateway and AAA virtual server configurations.

- **Added CVE-2025-6543** - NetScaler ADC/Gateway Buffer Overflow
- **References:**
  - https://nvd.nist.gov/vuln/detail/CVE-2025-6543
  - https://support.citrix.com/support-home/kbsearch/article?articleNumber=CTX694788
  - https://www.netscaler.com/blog/news/netscaler-critical-security-updates-for-cve-2025-6543-and-cve-2025-5777/

### Template Validation

I've validated this template locally?
- [x] YES
- [ ] NO

**Validation Results:**
```bash
nuclei -t http/cves/2025/CVE-2025-6543.yaml -validate
# Output: "All templates validated successfully"
```

#### Additional Details

**Vulnerability Details:**
- **Severity:** Critical (CVSS 9.2)
- **Affected Systems:** Citrix NetScaler ADC and NetScaler Gateway
- **Vulnerability Type:** Buffer Overflow (CWE-120)
- **Authentication:** Network access required (unauthenticated)
- **Attack Vector:** Network (Remote)
- **Configuration:** Gateway (VPN virtual server, ICA Proxy, CVPN, RDP Proxy) OR AAA virtual server

**Vulnerable Versions:**
- NetScaler 14.1: builds < 47.46
- NetScaler 13.1: builds < 59.19
- NetScaler 13.1FIPS: builds < 37.236
- NetScaler 13.1NDCPP: builds < 37.236
- NetScaler 12.1: builds < 55.328

**Template Features:**
- 3-stage detection process
- NetScaler identification via favicon, headers, and login pages
- Version-based vulnerability detection using regex patterns
- Multiple endpoint testing for comprehensive coverage
- Safe operation (no exploitation, only detection)

**Shodan Query:**
```
http.favicon.hash:-1292923998 OR http.favicon.hash:-1166125415
```

**Fofa Query:**
```
icon_hash="-1292923998" || icon_hash="-1166125415"
```

**Template Testing:**
- ✅ Validated with `nuclei -validate`
- ✅ Tested against non-NetScaler targets (no false positives)
- ✅ Makes 3 sequential requests as expected
- ✅ Proper version detection regex patterns
- ✅ Safe operation (detection only)

**HTTP Response Snippet (NetScaler Detection):**
```http
HTTP/1.1 200 OK
Server: NetScaler-Web/1.0
Set-Cookie: NSC_AAAC=xyz123; path=/
Content-Type: text/html

<!DOCTYPE html>
<html>
<head><title>NetScaler Gateway</title></head>
<body>
<div class="login-form">
    <h1>NetScaler Gateway</h1>
    <p>Version: NetScaler 13.1-58.15</p>
</div>
</body>
</html>
```

**Attack Vector Details:**
The vulnerability affects NetScaler devices configured as:
- Gateway (VPN virtual server, ICA Proxy, CVPN, RDP Proxy)
- AAA virtual server

Improper memory handling in these configurations allows attackers to trigger buffer overflows, potentially leading to denial of service and unintended control flow.

**Template Matchers:**
- NetScaler detection via multiple indicators (headers, body content, cookies)
- Version-based vulnerability assessment using comprehensive regex patterns
- Support for different NetScaler variants (standard, FIPS, NDCPP)
- Proper HTTP status code validation

**Debug Data:**
Template makes 3 sequential requests:
1. `GET /favicon.ico` - Check for NetScaler favicon
2. `GET /logon/LogonPoint/index.html` - NetScaler login page detection
3. `GET /nitro/v1/config/nsversion` - Management interface version check

Expected detection patterns include NetScaler version strings, NSC_ cookies, and specific HTML content indicating NetScaler presence.

**Safety Notes:**
- Detection-only template (no exploitation)
- Version-based assessment using official vulnerability data
- No persistent changes to target systems
- Comprehensive regex patterns to avoid false positives

**KEV Status:**
This vulnerability has been added to CISA's Known Exploited Vulnerabilities (KEV) catalog, indicating active exploitation in the wild.

### Additional References:

- [Nuclei Template Creation Guideline](https://nuclei.projectdiscovery.io/templating-guide/)
- [Nuclei Template Matcher Guideline](https://github.com/projectdiscovery/nuclei-templates/wiki/Unique-Template-Matchers)
- [Nuclei Template Contribution Guideline](https://github.com/projectdiscovery/nuclei-templates/blob/master/CONTRIBUTING.md)
- [PD-Community Discord server](https://discord.gg/projectdiscovery)

/claim #12914
