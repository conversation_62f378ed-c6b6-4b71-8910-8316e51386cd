id: unattached-vminstance-encryption-disabled

info:
  name: Encryption for VM Instance Disks - Disabled
  author: DhiyaneshDK
  severity: high
  description: |
    The encryption for VM instance disks in Alibaba Cloud is currently disabled. This means that the data stored on these disks is not automatically encrypted for enhanced security.
  reference:
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-ECS/encrypt-vm-instance-disks.html
    - https://www.alibabacloud.com/help/en/ecs/user-guide/encrypt-a-system-disk
    - https://www.alibabacloud.com/help/en/ecs/user-guide/encrypt-a-data-disk
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,ecs

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for(let InstanceId of iterate(template.instanceid)){
    set("instanceid", InstanceId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun ecs DescribeDisks --region $region

    extractors:
      - type: json
        name: instanceid
        internal: true
        json:
          - '.Disks.Disk[].InstanceId'

  - engine:
      - sh
      - bash

    source: |
      aliyun ecs DescribeDisks --InstanceId $instanceid --region $region

    matchers-condition: and
    matchers:
      - type: word
        words:
          - '"Encrypted": false'

      - type: word
        words:
          - '"InstanceId": ""'
        negative: true

    extractors:
      - type: dsl
        dsl:
          - 'instanceid + " Encryption for VM Instance Disks is Disabled"'
# digest: 490a00463044022065fc7c09f5a02fdbe644ceaf7fa05f05cc3ef357da65955c28917e0e7f18911c02205ff663b81f41ec4df06ecdaf43243c0b903c4971aca650730e6d51c918037c11:922c64590222798bb761d5b6d8e72950