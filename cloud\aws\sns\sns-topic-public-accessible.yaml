id: sns-topic-public-accessible

info:
  name: Public Access of SNS Topics via Policy
  author: <PERSON><PERSON>_<PERSON><PERSON>(#L4stPL4Y3R)
  severity: high
  description: |
    This template checks if Amazon SNS topics are configured to prevent public access via topic policies.
  reference:
    - https://docs.aws.amazon.com/sns/latest/api/API_GetTopicAttributes.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,sns,aws-cloud-config

flow: |
  code(1)
  for (let topicArn of iterate(template.topics)) {
    set("topicArn", topicArn)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws sns list-topics --query 'Topics[*].TopicArn'

    extractors:
      - type: json
        internal: true
        name: topics
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
      aws sns get-topic-attributes --topic-arn $topicArn --query Attributes.Policy --output text

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"Effect":"Allow"'

      - type: word
        part: body
        words:
          - '"Principal":{"AWS":"*"}'

      - type: regex
        part: body
        negative: true
        regex:
          - '"Condition"'

    extractors:
      - type: dsl
        dsl:
          - '"The SNS topic " + topicArn + " is publicly accessible via Policy"'
# digest: 490a0046304402207477e3b96fefe9ff0041e38fa1a327abd5cc0e56707254ebc55eb58f972fecbc02203fb1b080abc19fc5e2726b4eb7a6df47a964df150c88c2764151cc4b9f310a93:922c64590222798bb761d5b6d8e72950