id: password-policy-uppercase-unconfigured

info:
  name: RAM Password Policy requires atleast One Uppercase - Unconfigured
  author: DhiyaneshDK
  severity: medium
  description: |
    The Alibaba RAM password policy is not configured to require at least one uppercase letter in passwords. This misconfiguration may result in weak passwords, increasing the risk of unauthorized access.
  reference:
    - https://www.alibabacloud.com/help/en/ram/user-guide/configure-a-password-policy-for-ram-users
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RAM/uppercase-letter-password-policy.html
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-ram

variables:
  region: "cn-hangzhou"

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun ram GetPasswordPolicy --region $region

    matchers:
      - type: word
        name: policy
        words:
          - '"RequireUppercaseCharacters": false'

    extractors:
      - type: dsl
        dsl:
          - '"RAM Password Policy Does not contain One Uppercase "'
# digest: 4a0a00473045022100c499cd44ffa83f03e9c8fb263c637807c5715cb8e91ee70177758ccf4bb30a1b0220761da466ac954977004228aebfb485f970d35c750c42647a60d86e8b7788bac6:922c64590222798bb761d5b6d8e72950