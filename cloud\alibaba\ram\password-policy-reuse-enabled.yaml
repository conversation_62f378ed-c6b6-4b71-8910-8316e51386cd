id: password-policy-reuse-enabled

info:
  name: RAM Password Policy Reuse - Enabled
  author: D<PERSON>yaneshDK
  severity: medium
  description: |
    The Alibaba Cloud RAM (Resource Access Management) password policy reuse is disabled, meaning users are not restricted from reusing old passwords. This lack of a configured password history policy increases the risk of users reusing weak, previously compromised, or easily guessable passwords, which could lead to unauthorized access.
  reference:
    - https://www.alibabacloud.com/help/en/ram/user-guide/configure-a-password-policy-for-ram-users
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RAM/prevent-password-reuse-password-policy.html
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-ram

variables:
  region: "cn-hangzhou"

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun ram GetPasswordPolicy --region $region

    matchers:
      - type: word
        name: policy
        words:
          - '"PasswordReusePrevention": 0'

    extractors:
      - type: dsl
        dsl:
          - '"RAM Password Policy Allows Users Re-Use Old Password "'
# digest: 4a0a00473045022038d771b00b065b69552986f0255e955ada91cf894490f78ccd8dfd884cd45d54022100b2615506c64bcd841fef1b4d99c7c65d10acfce3feb2022405ab01f749c5834a:922c64590222798bb761d5b6d8e72950