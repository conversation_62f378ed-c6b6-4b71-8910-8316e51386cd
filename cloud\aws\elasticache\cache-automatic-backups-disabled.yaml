id: cache-automatic-backups-disabled

info:
  name: ElastiCache Automatic Backups - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that Amazon ElastiCache is configured to take automatic daily backups for Redis cache clusters.
  impact: |
    Disabling ElastiCache automatic backups increases the risk of data loss, as you won't have point-in-time recovery options in case of data corruption or accidental deletion.
  remediation: |
    enable automatic backups in the AWS Management Console for your ElastiCache Redis or Memcached cluster to ensure regular snapshots for data recovery.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/ElastiCache/enable-automatic-backups.html
    - https://docs.aws.amazon.com/AmazonElastiCache/latest/red-ug/backups-automatic.html
  tags: cloud,devops,aws,amazon,elasticache,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let ReplicationGroupId of iterate(template.replicationgroupids)){
    set("replicationgroup", ReplicationGroupId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws elasticache describe-replication-groups --region $region --output json --query 'ReplicationGroups[*].ReplicationGroupId'

    extractors:
      - type: json
        name: replicationgroupids
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws elasticache describe-replication-groups --replication-group-id $replicationgroup --query 'ReplicationGroups[*].SnapshotRetentionLimit' --region $region --output json

    matchers:
      - type: word
        words:
          - '0'

    extractors:
      - type: dsl
        dsl:
          - 'replicationgroup + " ElastiCache Automatic Backups is Disabled"'
# digest: 4a0a004730450221008465cb9d9adcf0af92d01705b4b1b96bb99e2d102c68031c3859cb59ba888f2602204d39d2985713c96993171906ce192e6266837bab101439560d3da3bc0f3a39ab:922c64590222798bb761d5b6d8e72950