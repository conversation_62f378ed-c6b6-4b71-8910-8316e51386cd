id: unencrypted-aws-ami
info:
  name: Unencrypted AWS AMI
  author: princechaddha
  severity: high
  description: |
    Ensure Amazon Machine Images (AMIs) are encrypted to meet data-at-rest encryption compliance and protect sensitive data.
  impact: |
    Unencrypted AMIs can expose sensitive data to unauthorized access, risking data breaches and non-compliance with data protection regulations.
  remediation: |
    Encrypt your AMIs using AWS managed keys or customer-managed keys in the AWS Key Management Service (KMS) to ensure data security.
  reference:
    - https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/AMIEncryption.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,ec2,aws-cloud-config
variables:
  region: "us-east-1"

flow: |
  code(1)
  for(let AmiName of iterate(template.amis)){
    set("ami", AmiName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws ec2 describe-images --region $region --owners self --output json --query 'Images[*].ImageId'

    extractors:
      - type: json
        name: amis
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws ec2 describe-images --region $region --image-ids $ami --query 'Images[*].BlockDeviceMappings[*].Ebs.Encrypted[]'

    matchers:
      - type: word
        words:
          - "false"

    extractors:
      - type: dsl
        dsl:
          - 'ami + " AMI is not encrypted"'
# digest: 4b0a00483046022100d2e495129ac87a1763fd07201f3c1e79f4ae175d89c6503f908c6f58e0cc9ef3022100da143e1eb20071e7d49cd28a4d6cadbe006692e7370304cdbf477ea85187dfc9:922c64590222798bb761d5b6d8e72950