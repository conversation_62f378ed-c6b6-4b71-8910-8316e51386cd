name: 📝 Template Checksum

on:
  push:
    branches:
      - main
    paths:
      - '**.yaml'
  workflow_dispatch:

env:
  CHECKSUM_FILE: 'templates-checksum.txt'

jobs:
  checksum:
    runs-on: ubuntu-latest
    if: github.repository == 'projectdiscovery/nuclei-templates'
    steps:
      - uses: actions/checkout@v5
      - uses: projectdiscovery/actions/generate/checksum@v1
        with:
          templates-directory: '${{ github.workspace }}'
          checksum-file: '${{ env.CHECKSUM_FILE }}'
      - run: git status
      - uses: projectdiscovery/actions/setup/git@v1
      - uses: projectdiscovery/actions/commit@v1
        with:
          files: '${{ env.CHECKSUM_FILE }}'
          message: 'chore: generate templates checksum 🤖'
      - name: Push changes
        run: |
          git pull origin $GITHUB_REF --rebase
          git push origin $GITHUB_REF
