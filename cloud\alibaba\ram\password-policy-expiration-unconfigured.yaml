id: password-policy-expiration-unconfigured

info:
  name: RAM Password Policy Expiration - Unconfigured
  author: DhiyaneshDK
  severity: medium
  description: |
    The Alibaba Cloud RAM Password Policy is unconfigured, leaving user accounts vulnerable to weak or expired passwords. This lack of a configured policy may lead to potential unauthorized access due to weak password management.
  reference:
    - https://www.alibabacloud.com/help/en/ram/user-guide/configure-a-password-policy-for-ram-users
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RAM/require-password-expiration-policy.html
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-ram

variables:
  region: "cn-hangzhou"

self-contained: true

code:
  - engine:
      - sh
      - bash

    source: |
      aliyun ram GetPasswordPolicy --region $region

    matchers-condition: and
    matchers:
      - type: regex
        regex:
          - '"MaxPasswordAge":\s*(9[1-9]|[1-9][0-9]{2,})'  # Matches values greater than 90

      - type: regex
        regex:
          - '"MaxPasswordAge":\s*(0|[1-9][0-9]?)'  # Excludes values between 0 and 90

    extractors:
      - type: dsl
        dsl:
          - '"RAM Password Policy Expiration is Greater than 90 Days "'
# digest: 490a0046304402207549bde74799cf714c472d8f8730a35eaa38a938692866df1904d0bd21ef7687022034a74ab5e9e73f0cf753ba6c4aaa75c4ec50913741389f2f63b7dcfdd63a2fc3:922c64590222798bb761d5b6d8e72950