id: mssql-audit-disabled

info:
  name: Microsoft SQLServer Database Instances - SQL Auditing Disabled
  author: DhiyaneshDK
  severity: high
  description: |
    SQL auditing is disabled on the MySQL database instances, meaning activities such as user queries and connection events are not logged. This may hinder the ability to track database activity, detect suspicious behavior, and comply with security auditing requirements.
  reference:
    - https://www.alibabacloud.com/help/en/rds/apsaradb-rds-for-mysql/use-the-sql-explorer-and-audit-feature-on-an-apsaradb-rds-for-mysql-instance
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RDS/enable-sql-server-audit-logs.html
  metadata:
    max-request: 2
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-rds

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for(let DBInstanceId of iterate(template.dbinstanceid)){
    set("instance", DBInstanceId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun rds DescribeDBInstances --Engine SQLServer --region $region

    extractors:
      - type: json
        name: dbinstanceid
        internal: true
        json:
          - '.Items.DBInstance[].DBInstanceId'

  - engine:
      - sh
      - bash

    source: |
      aliyun rds DescribeSQLCollectorPolicy --DBInstanceId $dbinstanceid --region $region

    matchers:
      - type: word
        words:
          - '"SQLCollectorStatus": "Disabled"'

    extractors:
      - type: dsl
        dsl:
          - 'instance + " Microsoft SQLServer Database Instances SQL Auditing Disabled "'
# digest: 4b0a00483046022100b12f7027262fdb72ae5cb4dda1491d4326f5236a6583ffc7ed3852b8810adc10022100d5ee7c6f58d43a06e8d8ae7eb554c286a67b07532971aa659d4ed79a8dfd5467:922c64590222798bb761d5b6d8e72950