id: rds-deletion-protection
info:
  name: RDS Deletion Protection
  author: princechaddha
  severity: high
  description: |
    Ensure Amazon RDS instances have Deletion Protection enabled to prevent accidental deletions.
  impact: |
    Without Deletion Protection, RDS instances can be inadvertently deleted, leading to potential data loss and service disruption.
  remediation: |
    Enable Deletion Protection for all Amazon RDS instances via the AWS Management Console or using the AWS CLI.
  reference:
    - https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_DeleteInstance.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config
variables:
  region: "ap-northeast-1"

flow: |
  code(1)
  for(let DBInstances of iterate(template.instances)){
    set("db", DBInstances)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --output json --query 'DBInstances[*].DBInstanceIdentifier'

    extractors:
      - type: json
        name: instances
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws rds describe-db-instances --region $region --db-instance-identifier $db --query 'DBInstances[*].DeletionProtection' --output json

    matchers:
      - type: word
        words:
          - 'false'

    extractors:
      - type: dsl
        dsl:
          - '"RDS Deletion protection feature is not enabled for RDS database instance " + db'
# digest: 4a0a00473045022018fbb94e0881e24fa32e1d836c422626ef525b7a589adcd55f2369a9c48dcf02022100e886ddd0c983785f04eb69bf4840dd572fd507ed675dc840713b6059a5d8d8c2:922c64590222798bb761d5b6d8e72950