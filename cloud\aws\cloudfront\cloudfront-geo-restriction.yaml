id: cloudfront-geo-restriction

info:
  name: CloudFront Geo Restriction - Not Enabled
  author: DhiyaneshDK
  severity: info
  description: |
    Ensure that geographic restriction is enabled for your Amazon CloudFront CDN distributions in order to allow or block viewers from specific locations (countries) from accessing your web content.
  impact: |
    Not enabling Geo Restriction in CloudFront exposes content to users from unauthorized regions, increasing the risk of content misuse, compliance violations, and potential security threats.
  remediation: |
    Enable Geo Restriction in CloudFront to control access to content based on geographic locations, ensuring only authorized users from designated regions can access specific resources.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/CloudFront/geo-restriction.html
    - https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/georestrictions.html
  tags: cloud,devops,aws,amazon,cloudfront,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DistributionListItemsId of iterate(template.distributions)){
    set("distribution", DistributionListItemsId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash

    source: |
      aws cloudfront list-distributions --output table --query 'DistributionList.Items[*].Id' --region $region --output json

    extractors:
      - type: json
        name: distributions
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws cloudfront get-distribution-config --id $distribution --query "DistributionConfig.Restrictions.GeoRestriction.RestrictionType" --region $region --output text

    matchers:
      - type: word
        words:
          - "none"

    extractors:
      - type: dsl
        dsl:
          - '"CloudFront Compress Objects Automatically " + distribution + " is Disabled"'
# digest: 4b0a00483046022100ae8efc4f803814a5fc310174a16c555a56e74965bbe71f368be319f2099aaf75022100f86d42da45940d19950c1cc38c99ea9bbc51db0be308ac041d61c03c32c58782:922c64590222798bb761d5b6d8e72950