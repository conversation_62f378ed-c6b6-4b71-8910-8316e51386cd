id: cloudtrail-public-buckets
info:
  name: Public CloudTrail Buckets
  author: princechaddha
  severity: critical
  description: |
    Identifies AWS CloudTrail S3 buckets that are publicly accessible, risking exposure of sensitive log data.
  impact: |
    Unauthorized access to CloudTrail logs can lead to data leakage, compromising the integrity and confidentiality of cloud operations.
  remediation: |
    Restrict S3 bucket access using bucket policies or IAM policies to ensure that CloudTrail logs are not publicly accessible.
  reference:
    - https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-find-log-files.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,s3,aws-cloud-config
variables:
  region: "ap-south-1"

flow: |
  code(1)
  for(let CloudTrail of iterate(template.cloudtrailname)){
    set("trail", CloudTrail)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws cloudtrail list-trails --region $region --query 'Trails[*].Name' --output json

    extractors:
      - type: json
        name: cloudtrailname
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws cloudtrail describe-trails --region $region --trail-name-list $trail --query 'trailList[*].IncludeGlobalServiceEvents'

    matchers:
      - type: word
        words:
          - "false"

    extractors:
      - type: dsl
        dsl:
          - '"CloudTrail trail" + trail + " is not configured to record API calls for AWS global services"'
# digest: 4a0a004730450221009199fc98dbbde4ee65e05efcf675a415ed8ec5b2e67e9804cb4a039f9cb6a59a02201ae662ec7da6b032d1dd62ba6ea0022da7bcec42c846387e7a62e53cc2cee466:922c64590222798bb761d5b6d8e72950