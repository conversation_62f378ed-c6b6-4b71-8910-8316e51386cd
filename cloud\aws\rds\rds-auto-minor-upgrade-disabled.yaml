id: rds-auto-minor-upgrade-disabled

info:
  name: RDS Auto Minor Version Upgrade - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that your Amazon RDS database instances have the Auto Minor Version Upgrade flag enabled in order to receive automatically minor engine upgrades during the specified maintenance window.
  impact: |
    The RDS instance may miss critical security patches and minor feature updates, increasing vulnerability to security risks and bugs.
  remediation: |
    Enable auto minor version upgrades for the RDS instance through the AWS Management Console, CLI, or API to ensure timely application of security patches and updates.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/RDS/rds-auto-minor-version-upgrade.html
    - http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_UpgradeDBInstance.Upgrading.html
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DBClusterIdentifier of iterate(template.dbclusters)){
    set("dbcluster", DBClusterIdentifier)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --output json --query 'DBInstances[?Engine==`mysql` || Engine==`postgres`].DBInstanceIdentifier | []' --output json

    extractors:
      - type: json
        name: dbclusters
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws rds describe-db-instances --region $region --db-instance-identifier $dbcluster --query 'DBInstances[*].AutoMinorVersionUpgrade' --output json

    matchers:
      - type: word
        words:
          - 'false'

    extractors:
      - type: dsl
        dsl:
          - 'dbcluster + " RDS Auto Minor Version Upgrade is Disabled"'
# digest: 490a0046304402207400d5163809ce8d3a2f2d31cfcb0d40177341f3eab12604df5afd272f5091c60220302d6c35d425a46e3a365984ef9b7258c3611883d532b4c71569aa05b4251787:922c64590222798bb761d5b6d8e72950