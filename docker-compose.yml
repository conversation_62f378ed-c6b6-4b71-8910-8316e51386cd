version: '3.8'

services:
  wordpress:
    image: wordpress:6.0-apache
    container_name: vulnerable-wordpress
    restart: always
    ports:
      - "8080:80"
    environment:
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress
      WORDPRESS_DB_NAME: wordpress
      WORDPRESS_DEBUG: 1
    volumes:
      - wordpress_data:/var/www/html
      - ./vulnerable-plugin:/var/www/html/wp-content/plugins/woocommerce-ultimate-gift-card
    depends_on:
      - db
    networks:
      - wordpress_network

  db:
    image: mysql:5.7
    container_name: wordpress-db
    restart: always
    environment:
      MYSQL_DATABASE: wordpress
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress
      MYSQL_ROOT_PASSWORD: rootpassword
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - wordpress_network

  nuclei-scanner:
    image: projectdiscovery/nuclei:latest
    container_name: nuclei-scanner
    volumes:
      - ./http/cves/2024/CVE-2024-8425.yaml:/templates/CVE-2024-8425.yaml
      - ./scan-results:/results
    command: >
      sh -c "
        echo 'Waiting for Word<PERSON>ress to be ready...' &&
        sleep 30 &&
        nuclei -t /templates/CVE-2024-8425.yaml -u http://wordpress:80 -debug -o /results/scan-results.txt
      "
    depends_on:
      - wordpress
    networks:
      - wordpress_network

volumes:
  wordpress_data:
  db_data:

networks:
  wordpress_network:
    driver: bridge
