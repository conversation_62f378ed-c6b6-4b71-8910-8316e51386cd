id: rds-instance-autoscaling-disabled

info:
  name: RDS Instance Storage AutoScaling - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that the Storage AutoScaling feature is enabled for your Amazon RDS database instances in order to provide dynamic scaling support for the database's storage based on your RDS application needs.
  impact: |
    The RDS instance may run out of storage, leading to potential application downtime or performance degradation due to lack of disk space.
  remediation: |
    Enable storage autoscaling for the RDS instance in the AWS Management Console or via CLI/API to automatically adjust storage capacity as needed.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/RDS/enable-rds-storage-autoscaling.html
    - https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_PIOPS.StorageTypes.html
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DBClusterIdentifier of iterate(template.dbclusters)){
    set("dbcluster", DBClusterIdentifier)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --output json --query 'DBInstances[?Engine==`mysql` || Engine==`postgres`].DBInstanceIdentifier | []'

    extractors:
      - type: json
        name: dbclusters
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws rds describe-db-instances --region $region --db-instance-identifier $dbcluster --query 'DBInstances[*].MaxAllocatedStorage' --output json

    matchers:
      - type: word
        words:
          - '[]'

    extractors:
      - type: dsl
        dsl:
          - 'dbcluster + " RDS Instance Storage AutoScaling is Disabled"'
# digest: 4b0a00483046022100c4214904851e1abc1c6deb9dd661cba059c3c73724311458790c515856cf681e022100ac7dde70bb47e7c9a08eacbdc59f4ef4381555fa5b5efec4260a2124b15fb5a8:922c64590222798bb761d5b6d8e72950