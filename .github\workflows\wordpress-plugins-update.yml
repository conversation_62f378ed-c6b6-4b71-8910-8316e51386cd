name: ✨ WordPress Plugins - Update

on:
  schedule:
    - cron: "0 0 * * *"

jobs:
  Update:
    runs-on: ubuntu-latest
    if: github.repository == 'projectdiscovery/nuclei-templates'
    steps:
      - name: Check out repository code
        uses: actions/checkout@v5

      - name: Install Python3
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - run: python -m pip install --upgrade pip
      - run: pip install -r .github/scripts/wordpress-plugins-update-requirements.txt

      - name: Update Templates
        run: python3 .github/scripts/wordpress-plugins-update.py

      - uses: projectdiscovery/actions/setup/git@v1

      - uses: projectdiscovery/actions/commit@v1
        with:
          files: '.'
          message: 'chore: update WordPress Plugins 🤖'

      - run: git pull origin $GITHUB_REF --rebase
      - run: git push origin $GITHUB_REF
