id: route53-dns-query-disabled

info:
  name: DNS Query Logging for Route 53 Hosted Zones - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Domain Name System Security Extensions (DNSSEC) represents a set of protocols that adds a layer of security to the Domain Name System (DNS) lookup and exchange processes by enabling DNS responses to be validated.
  impact: |
    Disabling DNS query logging for Route 53 hosted zones prevents visibility into DNS queries, making it difficult to detect suspicious activity, troubleshoot issues, or analyze traffic patterns.
  remediation: |
    Enable DNS query logging in the Route 53 console for the hosted zone to capture and store DNS queries, allowing for better monitoring and analysis of DNS traffic.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/Route53/enable-query-logging.html
    - https://docs.aws.amazon.com/Route53/latest/DeveloperGuide/monitoring-overview.html
  tags: cloud,devops,aws,amazon,route53,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let HostedZones of iterate(template.hostedzones)){
    set("hostedzone", HostedZones)
    code(2) && code(3)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws route53 list-hosted-zones --region $region --query "HostedZones[*].Id" --output json

    extractors:
      - type: json
        name: hostedzones
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws route53 get-hosted-zone --id $hostedzone --query "HostedZone.Config.PrivateZone" --region $region --output json

    matchers:
      - type: word
        words:
          - 'false'
        internal: true

  - engine:
      - sh
      - bash

    source: |
        aws route53 list-query-logging-configs --hosted-zone-id "$hostedzone" --query "QueryLoggingConfigs" --region $region --output json

    matchers:
      - type: word
        words:
          - '[]'

    extractors:
      - type: dsl
        dsl:
          - 'hostedzone + " DNSSEC Signing for Route 53 Hosted Zones is Disabled"'
# digest: 4a0a00473045022100acce037500a2f8789b1e4f0d79346cc90a712d201a6a5b5e57d536a963db28e90220249ff6436c358712755976982a8217932081f3f5cf9c71a48e19bba148755039:922c64590222798bb761d5b6d8e72950