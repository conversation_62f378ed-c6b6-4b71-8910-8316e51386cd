id: cache-redis-encryption-disabled

info:
  name: ElastiCache Redis In-Transit and At-Rest Encryption - Disabled
  author: DhiyaneshDK
  severity: high
  description: |
    Ensure that your Amazon ElastiCache Redis cache clusters are encrypted in order to meet security and compliance requirements.
  impact: |
    Sensitive data transmitted or stored in Redis could be exposed, leading to potential data breaches or unauthorized access.
  remediation: |
    Enable in-transit and at-rest encryption in the Redis cluster settings to protect sensitive data.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/ElastiCache/in-transit-and-at-rest-encryption.html
    - https://docs.aws.amazon.com/AmazonElastiCache/latest/red-ug/in-transit-encryption.html
    - https://docs.aws.amazon.com/AmazonElastiCache/latest/red-ug/at-rest-encryption.html
  tags: cloud,devops,aws,amazon,elasticache,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let ReplicationGroupId of iterate(template.replicationgroupids)){
    set("replicationgroup", ReplicationGroupId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws elasticache describe-replication-groups --region $region --output json --query 'ReplicationGroups[*].ReplicationGroupId'

    extractors:
      - type: json
        name: replicationgroupids
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws elasticache describe-replication-groups --region $region --replication-group-id $replicationgroup --query 'ReplicationGroups[*].[AtRestEncryptionEnabled,TransitEncryptionEnabled] | []' --output json

    matchers:
      - type: word
        words:
          - "false"

    extractors:
      - type: dsl
        dsl:
          - 'replicationgroup + " ElastiCache Redis In-Transit and At-Rest Encryption is Disabled"'
# digest: 4a0a0047304502201dd38f5bc616ffe3628f6f6f53827636fcff6ffaf03b2a7b9207c88376dc79e2022100f11960c7516037443a67fc2ee43e99baf683b701c9a2b3b22bb0e89467529b74:922c64590222798bb761d5b6d8e72950