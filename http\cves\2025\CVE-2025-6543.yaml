id: CVE-2025-6543

info:
  name: NetScaler ADC/Gateway - Buffer Overflow
  author: sushant6095
  severity: critical
  description: |
    NetScaler ADC and NetScaler Gateway contain a buffer overflow vulnerability caused by improper memory handling 
    in Gateway and AAA virtual server configurations. This allows attackers to cause denial of service and 
    unintended control flow with network access.
  reference:
    - https://nvd.nist.gov/vuln/detail/CVE-2025-6543
    - https://support.citrix.com/support-home/kbsearch/article?articleNumber=CTX694788
    - https://www.netscaler.com/blog/news/netscaler-critical-security-updates-for-cve-2025-6543-and-cve-2025-5777/
  classification:
    cpe: cpe:2.3:a:citrix:netscaler_application_delivery_controller:*:*:*:*:*:*:*:*
    cve-id: CVE-2025-6543
    cwe-id: CWE-120
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H
    cvss-score: 9.2
  metadata:
    verified: true
    max-request: 3
    vendor: citrix
    product: netscaler_application_delivery_controller
    shodan-query: 'http.favicon.hash:-1292923998 OR http.favicon.hash:-1166125415'
    fofa-query: 'icon_hash="-1292923998" || icon_hash="-1166125415"'
  tags: cve,cve2025,citrix,netscaler,buffer-overflow

http:
  - raw:
      # Stage 1: Detect NetScaler via favicon and headers
      - |
        GET /favicon.ico HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

      # Stage 2: Get NetScaler login page for version detection
      - |
        GET /logon/LogonPoint/index.html HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

      # Stage 3: Alternative version detection via management interface
      - |
        GET /nitro/v1/config/nsversion HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
        Authorization: Basic bnNyb290Om5zcm9vdA==

    matchers-condition: and
    matchers:
      # Verify NetScaler is detected
      - type: dsl
        name: netscaler_detected
        dsl:
          - "contains(body_2, 'NetScaler') || contains(body_2, 'Citrix') || contains(header_2, 'NSC_') || contains(body_3, 'NetScaler')"

      # Check for vulnerable version patterns
      - type: regex
        name: vulnerable_version
        part: body_2,body_3
        regex:
          # 14.1 builds < 47.46
          - "(?i)netscaler.*14\\.1[^0-9]*([0-4][0-9]|[0-3][0-9]\\.[0-9]+|4[0-6]\\.[0-3][0-9]|47\\.[0-3][0-9]|47\\.4[0-5])"
          # 13.1 builds < 59.19  
          - "(?i)netscaler.*13\\.1[^0-9]*([0-5][0-9]|[0-4][0-9]\\.[0-9]+|5[0-8]\\.[0-9]+|59\\.[0-1][0-8])"
          # 13.1FIPS builds < 37.236
          - "(?i)netscaler.*13\\.1.*fips[^0-9]*([0-3][0-6]|[0-2][0-9]\\.[0-9]+|3[0-6]\\.[0-9]+|37\\.[0-1][0-9][0-9]|37\\.2[0-2][0-9]|37\\.23[0-5])"
          # 13.1NDCPP builds < 37.236
          - "(?i)netscaler.*13\\.1.*ndcpp[^0-9]*([0-3][0-6]|[0-2][0-9]\\.[0-9]+|3[0-6]\\.[0-9]+|37\\.[0-1][0-9][0-9]|37\\.2[0-2][0-9]|37\\.23[0-5])"
          # 12.1 builds < 55.328
          - "(?i)netscaler.*12\\.1[^0-9]*([0-5][0-4]|[0-4][0-9]\\.[0-9]+|5[0-4]\\.[0-9]+|55\\.[0-2][0-9][0-9]|55\\.3[0-1][0-9]|55\\.32[0-7])"

      # Ensure proper HTTP responses
      - type: status
        status:
          - 200
          - 401
          - 403

    extractors:
      # Extract NetScaler version information
      - type: regex
        part: body_2,body_3
        name: netscaler_version
        group: 1
        regex:
          - "(?i)(netscaler.*?[0-9]+\\.[0-9]+[^\\s<>\"']*)"
          - "(?i)(ns[0-9]+\\.[0-9]+[^\\s<>\"']*)"

      # Extract server information
      - type: regex
        part: header_1,header_2,header_3
        name: server_info
        group: 1
        regex:
          - "Server: ([^\r\n]+)"
          - "Set-Cookie: (NSC_[^;]+)"

      # Extract any configuration hints
      - type: regex
        part: body_2,body_3
        name: config_hints
        group: 1
        regex:
          - "(Gateway|AAA|VPN|ICA Proxy|CVPN|RDP Proxy)"
