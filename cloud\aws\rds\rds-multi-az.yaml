id: rds-multi-az

info:
  name: RDS Multi-AZ - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that your Amazon RDS instances are using the Multi-AZ deployment configuration for high availability and automatic failover support, fully managed by AWS.
  impact: |
    The RDS instance lacks high availability and failover support, increasing the risk of downtime during instance failures or maintenance events.
  remediation: |
    Enable Multi-AZ deployment for the RDS instance in the AWS Management Console, CLI, or API to enhance availability and automatic failover.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/RDS/rds-multi-az.html
    - http://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Concepts.MultiAZ.html
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DBClusterIdentifier of iterate(template.dbclusters)){
    set("dbcluster", DBClusterIdentifier)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --output json --query 'DBInstances[*].DBInstanceIdentifier'

    extractors:
      - type: json
        name: dbclusters
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws rds describe-db-instances --region $region --db-instance-identifier $dbcluster --query 'DBInstances[*].MultiAZ'

    matchers:
      - type: word
        words:
          - 'false'

    extractors:
      - type: dsl
        dsl:
          - 'dbcluster + " RDS Multi-AZ is Disabled"'
# digest: 4a0a00473045022057b4f9fbb8844e06ba1d04ceea13f9d7d96a04c0b9a1bd9875e0d315a3e15e00022100debbbf8284e93334dca6c1b12d2a81d3bceecb75379c840dea06fa2faee0fbc4:922c64590222798bb761d5b6d8e72950