id: s3-public-read-acp

info:
  name: S3 Bucket with Public READ_ACP Access
  author: princechaddha
  severity: critical
  description: |
    Verifies that Amazon S3 buckets do not permit public 'READ_ACP' (LIST) access to anonymous users, protecting against unauthorized data exposure
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/s3api/get-bucket-acl.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,s3,aws-cloud-config

flow: |
  code(1)
  for(let bucketName of iterate(template.buckets)){
    set("bucket", bucketName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws s3api list-buckets --query 'Buckets[*].Name'

    extractors:
      - type: json # type of the extractor
        internal: true
        name: buckets
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws s3api get-bucket-acl --bucket $bucket --query 'Grants[?(Grantee.URI==`http://acs.amazonaws.com/groups/global/AllUsers`)]'

    matchers:
      - type: word
        words:
          - '"Permission": "READ_ACP"'

    extractors:
      - type: dsl
        dsl:
          - '"The S3 bucket " + bucket +" have public READ_ACP access"'
# digest: 4a0a00473045022018893dcd711c223bd1710b044ac2ca975c026f4355112bd79f87dfe5b64d6974022100c180ed08579d6a9f946cc76bb08f9b7eba75d318a174178f44093b074c6b2da8:922c64590222798bb761d5b6d8e72950