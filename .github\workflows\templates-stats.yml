name: 🗒 Templates Stats

on:
  push:
    tags:
      - '*'
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    if: github.repository == 'projectdiscovery/nuclei-templates'
    steps:
      - uses: actions/checkout@v5
        with: 
          fetch-depth: 0
      - uses: projectdiscovery/actions/templates/stats@v1
        with:
          path: '${{ github.workspace }}'
          output: 'TEMPLATES-STATS.md'
      - uses: projectdiscovery/actions/templates/stats@v1
        with:
          path: '${{ github.workspace }}'
          output: 'TEMPLATES-STATS.json'
          args: '-json'
      - uses: projectdiscovery/actions/templates/stats@v1
        with:
          path: '${{ github.workspace }}'
          output: 'TOP-10.md'
          args: '-top 10'
      - uses: projectdiscovery/actions/setup/git@v1
      - uses: projectdiscovery/actions/commit@v1
        with:
          files: |
            TEMPLATES-STATS.*
            TOP-10.md
          message: 'chore: generate templates stats 🤖'
      - uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}