id: password-policy-lowercase-unconfigured

info:
  name: RAM Password Policy requires atleast One Lowercase - Unconfigured
  author: DhiyaneshDK
  severity: medium
  description: |
    The Alibaba RAM password policy is not configured to require at least one lowercase letter in passwords. This misconfiguration may result in weak passwords, increasing the risk of unauthorized access.
  reference:
    - https://www.alibabacloud.com/help/en/ram/user-guide/configure-a-password-policy-for-ram-users
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RAM/lowercase-letter-password-policy.html
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-ram

variables:
  region: "cn-hangzhou"

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun ram GetPasswordPolicy --region $region

    matchers:
      - type: word
        name: policy
        words:
          - '"RequireLowercaseCharacters": false'

    extractors:
      - type: dsl
        dsl:
          - '"RAM Password Policy Does not contain One Lowercase "'
# digest: 4a0a00473045022058492c7376d065fd9a4bd5b0f6c3de42c2a7b15b7a4dff29f157fc0e69ec3e2f022100e845dd85858662f51084f880b16dc238a6c7f498a69cb478e8815f3cd9ede17d:922c64590222798bb761d5b6d8e72950