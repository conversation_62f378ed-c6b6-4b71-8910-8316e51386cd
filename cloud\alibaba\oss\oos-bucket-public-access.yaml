id: oos-bucket-public-access

info:
  name: OSS Bucket Public Accessible
  author: DhiyaneshDK
  severity: high
  description: |
    Ensure that your Object Storage Service (OSS) buckets are not publicly accessible to the Internet in order to protect against unauthorized access. Allowing public, anonymous access through bucket ACLs and bucket policies gives everyone the ability to access bucket contents.
  reference:
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-OSS/publicly-accessible-oss-bucket.html
    - https://www.alibabacloud.com/help/en/oss/user-guide/block-public-access
  metadata:
    max-request: 2
    verified: true
  tags: cloud,devops,aliyun,alibaba,aliyun-cloud-config,alibaba-oss

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for (let BucketName of iterate(template.bucketname)) {
    set("bucket", BucketName)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash

    source: |
      aliyun oss ls --region $region

    extractors:
      - type: regex
        name: bucketname
        internal: true
        regex:
          - 'oss://([a-zA-Z0-9-]+)'

  - engine:
      - sh
      - bash

    source: |
      aliyun oss stat $bucket --region $region

    matchers-condition: and
    matchers:
      - type: word
        words:
          - 'private'
        negative: true

      - type: word
        words:
          - 'public-read'
          - 'public-read-write'
        condition: or

    extractors:
      - type: dsl
        dsl:
          - 'bucket + " OSS Bucket is Public Accessible "'
# digest: 4b0a00483046022100ebe3f235d7b322069926037bb46627b158f4c3aaef6fce8f53d5b7796c00d1f6022100a423932d5a987382196d74de46a97ba1dcb4b25980a28181945ee69c70512e45:922c64590222798bb761d5b6d8e72950