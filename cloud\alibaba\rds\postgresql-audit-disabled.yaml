id: postgresql-audit-disabled

info:
  name: PostgreSQL Database Instances - SQL Auditing Disabled
  author: DhiyaneshDK
  severity: high
  description: |
    SQL auditing is disabled on the PostgreSQL database instances, meaning activities such as user queries and connection events are not logged. This may hinder the ability to track database activity, detect suspicious behavior, and comply with security auditing requirements.
  reference:
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RDS/enable-postgres-audit-logs.html
    - https://www.alibabacloud.com/help/en/rds/apsaradb-rds-for-postgresql/use-the-sql-explorer-and-audit-feature-on-an-apsaradb-rds-for-postgresql-instance
  metadata:
    max-request: 2
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-rds

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for(let DBInstanceId of iterate(template.dbinstanceid)){
    set("instance", DBInstanceId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun rds DescribeDBInstances --Engine PostgreSQL --region $region

    extractors:
      - type: json
        name: dbinstanceid
        internal: true
        json:
          - '.Items.DBInstance[].DBInstanceId'

  - engine:
      - sh
      - bash

    source: |
      aliyun rds DescribeSQLCollectorPolicy --DBInstanceId $dbinstanceid --region $region

    matchers:
      - type: word
        words:
          - '"SQLCollectorStatus": "Disabled"'

    extractors:
      - type: dsl
        dsl:
          - 'instance + " PostgreSQL Database Instances SQL Auditing Disabled "'
# digest: 4a0a00473045022100ce7c455a2ed9fdab36bc318389f3676c5b38da1642eb7c681c2298b3f2dcc024022057f0fb86ab309677a3c7e9f5ae172202b612be0712c5368c996c4428df8a4c70:922c64590222798bb761d5b6d8e72950