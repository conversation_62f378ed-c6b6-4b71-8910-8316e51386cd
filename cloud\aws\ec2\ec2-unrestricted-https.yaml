id: ec2-unrestricted-https
info:
  name: Unrestricted HTTPs on EC2
  author: princechaddha
  severity: critical
  description: |
    Checks for inbound rules in EC2 security groups allowing unrestricted access (0.0.0.0/0) to TCP port 443, increasing exposure to potential breaches.
  impact: |
    Unrestricted access to TCP port 443 can lead to unauthorized data exposure and increases the risk of security breaches.
  remediation: |
    Restrict inbound traffic on TCP port 443 to only necessary IP addresses, adhering to the principle of least privilege.
  reference:
    - https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-security-groups.html
  tags: cloud,devops,aws,amazon,ec2,aws-cloud-config

variables:
  region: "us-east-1"

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws ec2 describe-security-groups --region $region --filters Name=ip-permission.from-port,Values=443 Name=ip-permission.to-port,Values=443 Name=ip-permission.cidr,Values='0.0.0.0/0' --query 'SecurityGroups[*].GroupId' --output json

    extractors:
      - type: json
        name: securitygroup
        internal: true
        json:
          - '.[]'

      - type: dsl
        dsl:
          - 'securitygroup + " security group(s) alows unrestricted access (0.0.0.0/0 or ::/0) on TCP port 443"'
# digest: 4b0a00483046022100ed1d020849f7e7bf9c39cfc00d17bb3bd2c27309f8d2fc2deb1705f3affa25470221009e2c2adcd2de63f1c326c2716ffe1ab90eedf9f55978f71cc45f7d0e0db4c2e1:922c64590222798bb761d5b6d8e72950