id: s3-public-write-acp
info:
  name: S3 Bucket with Public WRITE_ACP Access
  author: princechaddha
  severity: critical
  description: |
    Checks if Amazon S3 buckets are secured against public WRITE_ACP access, preventing unauthorized modifications to access control permissions.
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/s3api/get-bucket-acl.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,s3,aws-cloud-config

flow: |
  code(1)
  for(let bucketName of iterate(template.buckets)){
    set("bucket", bucketName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws s3api list-buckets --query 'Buckets[*].Name'

    extractors:
      - type: json # type of the extractor
        internal: true
        name: buckets
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
        aws s3api get-bucket-acl --bucket $bucket --query 'Grants[?(Grantee.URI==`http://acs.amazonaws.com/groups/global/AllUsers`)]'

    matchers:
      - type: word
        words:
          - '"Permission": "WRITE_ACP"'

    extractors:
      - type: dsl
        dsl:
          - '"The S3 bucket " + bucket +" have public WRITE_ACP access"'
# digest: 490a004630440220375eb24e307104f858dc824f8fa6252967178f9386b42fa600544f6e120c552c022061bc51077ad9e5ae712c7bebd797fc430322c97bf8d90a4ffba772d9e4067805:922c64590222798bb761d5b6d8e72950