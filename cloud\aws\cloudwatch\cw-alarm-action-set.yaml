id: cw-alarm-action-set
info:
  name: CloudWatch Alarm Action Not Set
  author: princechaddha
  severity: medium
  description: |
    Ensure Amazon CloudWatch alarms have actions configured for the ALARM state to automate response to incidents.
  impact: |
    Without actions, CloudWatch alarms may not trigger automated incident response or notifications, potentially delaying mitigation.
  remediation: |
    Configure at least one action for each CloudWatch alarm to ensure timely response to monitored issues.
  reference:
    - https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/AlarmThatSendsEmail.html
  metadata:
    max-request: 2
  tags: cloud,devops,aws,amazon,cloudwatch,aws-cloud-config
variables:
  region: "us-east-1"

flow: |
  code(1)
  for(let AlarmName of iterate(template.alarms)){
    set("alarm", AlarmName)
    code(2)
  }

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws cloudwatch describe-alarms --region $region --query 'MetricAlarms[].AlarmName' --output json

    extractors:
      - type: json
        name: alarms
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash
    source: |
         aws cloudwatch describe-alarms --region $region --alarm-names "$alarm" --query 'MetricAlarms[*].AlarmActions[]' --output json

    matchers:
      - type: word
        words:
          - "[]"

    extractors:
      - type: dsl
        dsl:
          - '"The Amazon CloudWatch " + alarm +" is not configured with any actions for the ALARM state."'
# digest: 4a0a00473045022100ec74e58a4ecafde302484c27c761d114bc2c7babcae0491458a94cf6a12acb6d02206af1f79eb9666f2fbd805e82bd2ceb14b75a51fcde66b1105180ff55021ab68a:922c64590222798bb761d5b6d8e72950