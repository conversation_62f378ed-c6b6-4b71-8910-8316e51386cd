<?php
/**
 * Plugin Name: WooCommerce Ultimate Gift Card (Vulnerable Version)
 * Plugin URI: https://example.com/
 * Description: Vulnerable version for CVE-2024-8425 testing - DO NOT USE IN PRODUCTION
 * Version: 2.6.0
 * Author: Test Author
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add AJAX handler for unauthenticated users
add_action('wp_ajax_nopriv_mwb_wgm_preview_mail', 'mwb_wgm_preview_mail_handler');
add_action('wp_ajax_mwb_wgm_preview_mail', 'mwb_wgm_preview_mail_handler');

/**
 * Vulnerable file upload handler - CVE-2024-8425
 * This function has insufficient file type validation
 */
function mwb_wgm_preview_mail_handler() {
    // Simulate the vulnerable behavior
    if (isset($_FILES['file'])) {
        $upload_dir = wp_upload_dir();
        $target_dir = $upload_dir['basedir'] . '/mwb_browse/';
        
        // Create directory if it doesn't exist
        if (!file_exists($target_dir)) {
            wp_mkdir_p($target_dir);
        }
        
        $target_file = $target_dir . basename($_FILES['file']['name']);
        
        // Vulnerable: No proper file type validation
        // Only checks MIME type which can be spoofed
        $allowed_types = array('image/gif', 'image/jpeg', 'image/png');

        if (in_array($_FILES['file']['type'], $allowed_types)) {
            // Additional safety: prevent PHP execution in test environment
            $file_extension = pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION);
            if (strtolower($file_extension) === 'php') {
                // Rename PHP files to .txt for safety
                $safe_filename = pathinfo($_FILES['file']['name'], PATHINFO_FILENAME) . '.txt';
                $target_file = $target_dir . $safe_filename;
            }

            if (move_uploaded_file($_FILES['file']['tmp_name'], $target_file)) {
                wp_send_json_success(array(
                    'message' => 'File uploaded successfully',
                    'file_url' => $upload_dir['baseurl'] . '/mwb_browse/' . basename($target_file)
                ));
            } else {
                wp_send_json_error('Upload failed');
            }
        } else {
            wp_send_json_error('Invalid file type');
        }
    }
    
    wp_send_json_error('No file provided');
}

// Enqueue admin styles (for detection)
add_action('admin_enqueue_scripts', 'mwb_wgm_admin_styles');

function mwb_wgm_admin_styles() {
    wp_enqueue_style(
        'woocommerce-ultimate-gift-card-admin',
        plugin_dir_url(__FILE__) . 'assets/css/woocommerce-ultimate-gift-card-admin.css',
        array(),
        '2.6.0'
    );
}
?>
