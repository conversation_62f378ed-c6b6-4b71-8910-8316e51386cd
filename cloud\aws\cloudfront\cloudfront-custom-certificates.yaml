id: cloudfront-custom-certificates

info:
  name: Cloudfront Custom SSL/TLS Certificates - In Use
  author: DhiyaneshDK
  severity: medium
  description: |
    Ensure that your Amazon CloudFront distributions are configured to use a custom SSL/TLS certificate instead of the default one.
  impact: |
    Failing to use custom SSL/TLS certificates in CloudFront can result in trust issues with end users, exposing your web content to man-in-the-middle attacks and potentially damaging your brand's reputation due to untrusted connection warnings.
  remediation: |
    Configure your Amazon CloudFront distribution to use custom SSL/TLS certificates to ensure secure and trusted connections for your users, enhancing data protection and maintaining brand integrity.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/CloudFront/cloudfront-distro-custom-tls.html
    - https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/CNAMEs.html
  tags: cloud,devops,aws,amazon,cloudfront,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DistributionListItemsId of iterate(template.distributions)){
    set("distribution", DistributionListItemsId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash

    source: |
      aws cloudfront list-distributions --output table --query 'DistributionList.Items[*].Id' --region $region --output json

    extractors:
      - type: json
        name: distributions
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws cloudfront get-distribution --region $region --id $distribution --query 'Distribution.DistributionConfig.ViewerCertificate.CloudFrontDefaultCertificate' --output text

    matchers:
      - type: word
        words:
          - "False"

    extractors:
      - type: dsl
        dsl:
          - '"Cloudfront Custom SSL/TLS Certificates " + distribution + " In Use"'
# digest: 4b0a00483046022100f4348f4cb85e838f5be2e607133e43a5b3a4c72fdc94fd193eded1206bd6e1eb022100f883292e3ccc805f74f4d647813d0575945275eb447ae0f08901b9c3700fb162:922c64590222798bb761d5b6d8e72950