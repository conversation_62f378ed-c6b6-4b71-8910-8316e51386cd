id: rds-event-notify
info:
  name: RDS Event Notification Absence
  author: princechaddha
  severity: medium
  description: |
    Checks for the activation of event notifications for Amazon RDS instances to monitor significant database events.
  impact: |
    Without event notifications, there's a risk of missing critical database events, impacting operational awareness and incident response.
  remediation: |
    Enable event notifications in Amazon RDS by creating an event subscription with Amazon SNS to receive notifications.
  reference:
    - https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Events.html
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config

variables:
  region: "ap-northeast-1"

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-event-subscriptions --region $region --query 'EventSubscriptionsList'

    matchers:
      - type: word
        words:
          - '[]'

    extractors:
      - type: dsl
        dsl:
          - '"No event notifications for RDS resources in " + region + " AWS region"'
# digest: 4a0a00473045022100c353a0f7695375fad5268b3bf767c0d48ea8b7ebff5d02c162d141355464781e02201dbe78f729940729cc1625b7c25d8a3729630f7ebe050886dbcda9fb392e7b4e:922c64590222798bb761d5b6d8e72950