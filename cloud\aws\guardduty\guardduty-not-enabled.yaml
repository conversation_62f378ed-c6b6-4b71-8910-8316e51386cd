id: guardduty-not-enabled

info:
  name: GuardDuty Not Enabled
  author: DhiyaneshDK
  severity: info
  description: |
    Ensure that Amazon GuardDuty service is currently enabled in all regions in order to protect your AWS environment and infrastructure (AWS accounts and resources, IAM credentials, guest operating systems, applications, etc) against security threats.
  impact: |
    GuardDuty disabled leaves your AWS environment vulnerable to undetected threats, such as unauthorized access, anomalous activities, and potential security breaches, compromising the overall security posture.
  remediation: |
    Enable GuardDuty to continuously monitor and detect security threats in your AWS environment.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/GuardDuty/guardduty-enabled.html
    - https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_settingup.html
  tags: cloud,devops,aws,amazon,guardduty,aws-cloud-config

variables:
  region: "us-west-2"

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws guardduty list-detectors --region $region --query 'DetectorIds' --output json

    matchers:
      - type: word
        words:
          - "[]"

    extractors:
      - type: dsl
        dsl:
          - '"GuardDuty Is Not Enabled"'
# digest: 4a0a00473045022008f8251bf1a69a5be73f1f629d55e8c729583fb1621fc3a9f273e443c28a991b022100c4b3ca95cd7523f3566f52a5d285a46319f76ab496f05a66cf9a436255c212a6:922c64590222798bb761d5b6d8e72950