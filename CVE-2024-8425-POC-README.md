# CVE-2024-8425 - W<PERSON>Commerce Ultimate Gift Card File Upload PoC

## Overview

This repository contains a comprehensive Proof of Concept (PoC) for CVE-2024-8425, an unauthenticated file upload vulnerability in the WooCommerce Ultimate Gift Card plugin for WordPress (versions ≤ 2.6.0).

## Vulnerability Details

- **CVE ID**: CVE-2024-8425
- **Severity**: Critical (CVSS Score: 9.8)
- **Affected Plugin**: WooCommerce Ultimate Gift Card
- **Affected Versions**: All versions up to and including 2.6.0
- **Vulnerability Type**: Unrestricted File Upload (CWE-434)
- **Authentication Required**: No (Unauthenticated)

### Technical Details

The vulnerability exists in two functions:
1. `mwb_wgm_preview_mail` - Email preview functionality
2. `mwb_wgm_woocommerce_add_cart_item_data` - Cart item data processing

Both functions have insufficient file type validation, allowing attackers to upload arbitrary files by spoofing MIME types.

## PoC Environment Components

### 1. Nuclei Template (`http/cves/2024/CVE-2024-8425.yaml`)

A comprehensive 3-stage detection template:
- **Stage 1**: Plugin detection via CSS file
- **Stage 2**: File upload exploitation
- **Stage 3**: Upload verification and execution

### 2. Docker Environment

Complete testing environment with:
- WordPress 6.0 with vulnerable plugin
- MySQL database
- Nuclei scanner container
- Automated testing workflow

### 3. Vulnerable Plugin Simulation

Recreated vulnerable plugin behavior for safe testing:
- Mimics the exact vulnerability conditions
- Safe for testing environments
- Includes detection markers

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Nuclei (for standalone testing)

### Setup Instructions

1. **Clone and Setup**:
   ```bash
   git clone <repository>
   cd nuclei-templates
   ```

2. **Start Vulnerable Environment**:
   ```bash
   docker-compose up -d
   ```

3. **Wait for WordPress Setup** (2-3 minutes):
   ```bash
   docker-compose logs -f wordpress
   ```

4. **Run Nuclei Template**:
   ```bash
   nuclei -t http/cves/2024/CVE-2024-8425.yaml -u http://localhost:8080
   ```

### Expected Results

**Vulnerable Target**:
```
[CVE-2024-8425] [http] [critical] http://localhost:8080/wp-admin/admin-ajax.php?action=mwb_wgm_preview_mail
```

**Non-Vulnerable Target**:
```
No results found
```

## Testing Scenarios

### 1. Automated Docker Testing

```bash
# Start environment
docker-compose up -d

# Check scan results
docker-compose logs nuclei-scanner
cat scan-results/scan-results.txt
```

### 2. Manual Testing

```bash
# Test template validation
nuclei -t http/cves/2024/CVE-2024-8425.yaml -validate

# Test against target
nuclei -t http/cves/2024/CVE-2024-8425.yaml -u https://target.com -debug

# Test with custom payload
nuclei -t http/cves/2024/CVE-2024-8425.yaml -u https://target.com -var filename=custom_test
```

### 3. Debug Mode Testing

```bash
nuclei -t http/cves/2024/CVE-2024-8425.yaml -u http://localhost:8080 -debug -v
```

## Debug Data

### Template Validation Output

```
[VER] Started metrics server at localhost:9092
[INF] All templates validated successfully
```

### Successful Detection Output

```
[INF] [CVE-2024-8425] Dumped HTTP request for http://target/wp-content/plugins/woocommerce-ultimate-gift-card/assets/css/woocommerce-ultimate-gift-card-admin.css
[INF] [CVE-2024-8425] Dumped HTTP request for http://target/wp-admin/admin-ajax.php?action=mwb_wgm_preview_mail
[INF] [CVE-2024-8425] Dumped HTTP request for http://target/wp-content/uploads/mwb_browse/test.php
```

## Cleanup

```bash
# Stop and remove containers
docker-compose down -v

# Remove uploaded test files (if any)
rm -rf scan-results/
```

## Security Considerations

⚠️ **WARNING**: This PoC is for educational and authorized testing purposes only.

- Only test on systems you own or have explicit permission to test
- The vulnerable plugin simulation should never be used in production
- Uploaded test files are automatically cleaned up via `unlink(__FILE__)`

## Mitigation

- Update WooCommerce Ultimate Gift Card plugin to version > 2.6.0
- Implement proper file type validation
- Use WordPress's built-in file upload security functions
- Restrict file upload directories with proper permissions

## References

- [CVE-2024-8425 - MITRE](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-8425)
- [CVE-2024-8425 - NVD](https://nvd.nist.gov/vuln/detail/CVE-2024-8425)
- [Original PoC](https://github.com/KTN1990/CVE-2024-8425)
- [WordPress Security Best Practices](https://wordpress.org/support/article/hardening-wordpress/)

## Contributing

This PoC was created for the Nuclei Templates Community Rewards Program. For issues or improvements, please submit a pull request with detailed testing results.
