id: ec2-unrestricted-smtp
info:
  name: Unrestricted SMTP Access in EC2
  author: princechaddha
  severity: critical
  description: |
    Identifies unrestricted inbound access on TCP port 25 for EC2 security groups, which increases the risk of SMTP-related attacks.
  impact: |
    Allowing unrestricted SMTP access can lead to spamming, mail relay abuse, and potentially compromise mail servers.
  remediation: |
    Restrict TCP port 25 access to known, necessary IP addresses only. Avoid using 0.0.0.0/0 or ::/0 in security group rules.
  reference:
    - https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/authorizing-access-to-an-instance.html
  tags: cloud,devops,aws,amazon,ec2,aws-cloud-config

variables:
  region: "us-east-1"

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws ec2 describe-security-groups --region $region --filters Name=ip-permission.from-port,Values=25 Name=ip-permission.to-port,Values=25 Name=ip-permission.cidr,Values='0.0.0.0/0' --output json --query 'SecurityGroups[*].GroupId'

    extractors:
      - type: json
        name: securitygroup
        internal: true
        json:
          - '.[]'

      - type: dsl
        dsl:
          - 'securitygroup + " security group(s) alows unrestricted access (0.0.0.0/0 or ::/0) on TCP port 25"'
# digest: 490a0046304402203bfae3cd9ef508cefed1bc32dd02dfc1175acd449fd1ba9bf16d82338cc2f3110220134c0b49bd49e77281b4011b48739cc645fc2223c80eace3af80ea52fe36f24e:922c64590222798bb761d5b6d8e72950