id: ssh-hostbasedauth-disabled

info:
  name: Ensure SSH HostbasedAuthentication - Disabled
  author: Th3l0newolf
  severity: high
  description: |
    The HostbasedAuthentication parameter determines whether SSH authentication is permitted using trusted hosts, based on entries in .rhosts or /etc/hosts.equiv, in combination with successful public key authentication from the client host.
  remediation: |
    Edit /etc/ssh/sshd_config to set 'HostbasedAuthentication no' and restart SSH service with 'sudo systemctl restart sshd'.
  reference:
    - https://www.cisecurity.org/benchmark/ubuntu_linux
    - https://docs.datadoghq.com/security/default_rules/def-000-fqw/
  metadata:
    verified: true
  tags: cis,ssh,linux,audit,ubuntu,benchmark

self-contained: true

code:
  - engine:
      - bash

    args:
      - "-c"
      - |
        SSHD_CONFIG="/etc/ssh/sshd_config"
        # Check if the parameter is set to yes (bad)
        if grep -i '^\s*HostbasedAuthentication\s\+yes' "$SSHD_CONFIG" >/dev/null; then
          current_value=$(grep -i '^\s*HostbasedAuthentication' "$SSHD_CONFIG" | head -1 | tr -d '\n')
          echo "[cis-ssh-hostbasedauth-disabled:Policy-Fail] [$current_value] [CIS_FAIL] [medium]"
        else
          echo "[cis-ssh-hostbasedauth-disabled:Policy-Pass] [HostbasedAuthentication no or not set] [CIS_PASS] [medium]"
        fi

    matchers:
      - type: word
        name: policy-pass
        words:
          - "Policy-Pass"

      - type: word
        name: policy-fail
        words:
          - "Policy-Fail"
# digest: 490a00463044022013edf98f527f0171254a8ef0aafc2ac836f099ba47c7556913bc253e21a49878022072275743c7347b438d73fbc3b1b65b9498bea608ed176ab9925ba71139a92e2d:922c64590222798bb761d5b6d8e72950