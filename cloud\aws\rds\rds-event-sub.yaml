id: rds-event-sub
info:
  name: RDS Security Group Event Notifications
  author: princechaddha
  severity: high
  description: |
    Ensure RDS event notification subscriptions are active for database security group events to monitor and react to changes in security configurations.
  impact: |
    Without notifications for security group events, unauthorized changes may go unnoticed, potentially leading to security breaches or data exposure.
  remediation: |
    Enable Amazon RDS event notification subscriptions for relevant database security group events through the AWS Management Console or AWS CLI.
  reference:
    - https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Events.html
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config

variables:
  region: "ap-northeast-1"

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-event-subscriptions --region $region --query "EventSubscriptionsList[?SourceType == 'db-security-group'].CustSubscriptionId"

    matchers:
      - type: word
        words:
          - '[]'

    extractors:
      - type: dsl
        dsl:
          - '"There are no Amazon RDS event subscriptions created for database security groups available in " + region + " AWS region."'
# digest: 490a0046304402206dbf3f2479027e51894a7f8d6318a01a9ad7ee4ef8bf8887c796442a9bfa08b902201858d0922a1fead0ab9008d7f855f368757803f567c87f7475647200dcc91768:922c64590222798bb761d5b6d8e72950