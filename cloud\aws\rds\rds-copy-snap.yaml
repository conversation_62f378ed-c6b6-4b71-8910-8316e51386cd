id: rds-copy-snap

info:
  name: RDS Copy Tags to Snapshots - Disabled
  author: DhiyaneshDK
  severity: low
  description: |
    Ensure that your Amazon RDS database instances make use of the Copy Tags to Snapshots feature in order to allow tags set on your database instances to be automatically copied to any automated or manual database snapshots that are created from these RDS instances.
  impact: |
    Tags are not copied to snapshots, making it harder to track, manage, and identify snapshots for cost allocation, compliance, or organization.
  remediation: |
    Enable the "Copy Tags to Snapshots" option for the RDS instance in the AWS Management Console, CLI, or API to ensure that tags are automatically applied to any created snapshots.
  reference:
    - https://www.trendmicro.com/cloudoneconformity-staging/knowledge-base/aws/RDS/copy-tags-to-snapshot.html
    - https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Tagging.html
  tags: cloud,devops,aws,amazon,rds,aws-cloud-config

variables:
  region: "us-west-2"

flow: |
  code(1)
  for(let DBClusterIdentifier of iterate(template.dbclusters)){
    set("dbcluster", DBClusterIdentifier)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aws rds describe-db-instances --region $region --output json --query 'DBInstances[?Engine==`mysql` || Engine==`postgres`].DBInstanceIdentifier | []'

    extractors:
      - type: json
        name: dbclusters
        internal: true
        json:
          - '.[]'

  - engine:
      - sh
      - bash

    source: |
        aws rds describe-db-instances --region $region --db-instance-identifier $dbcluster --query 'DBInstances[*].CopyTagsToSnapshot'

    matchers:
      - type: word
        words:
          - 'false'

    extractors:
      - type: dsl
        dsl:
          - '"Copy Tags To Snapshot is not enable for instance " + dbcluster'
# digest: 4b0a004830460221009d904b350989755e3a37dde877817c5020331fbec3570485e37f76a31e42032b022100b422a3a28f78eb7ba2fc15d9e03d9b7d1bd9b49948bee87b4c9af85ad31bad02:922c64590222798bb761d5b6d8e72950