id: ec2-unrestricted-http
info:
  name: Unrestricted HTTP on EC2
  author: princechaddha
  severity: critical
  description: |
    Checks for inbound rules in EC2 security groups allowing unrestricted access (0.0.0.0/0) to TCP port 80, increasing exposure to potential breaches.
  impact: |
    Unrestricted access to TCP port 80 can lead to unauthorized data exposure and increases the risk of security breaches.
  remediation: |
    Restrict inbound traffic on TCP port 80 to only necessary IP addresses, adhering to the principle of least privilege.
  reference:
    - https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-security-groups.html
  tags: cloud,devops,aws,amazon,ec2,aws-cloud-config

variables:
  region: "us-east-1"

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws ec2 describe-security-groups --region $region --filters Name=ip-permission.from-port,Values=80 Name=ip-permission.to-port,Values=80 Name=ip-permission.cidr,Values='0.0.0.0/0' --query 'SecurityGroups[*].GroupId' --output json

    extractors:
      - type: json
        name: securitygroup
        internal: true
        json:
          - '.[]'

      - type: dsl
        dsl:
          - 'securitygroup + " security group(s) alows unrestricted access (0.0.0.0/0 or ::/0) on TCP port 80"'
# digest: 4a0a004730450221009f14fce67c7ed1c87516a472bd9ff6c86384a90af8fd5fef53ba4705ae402c3802205278dc94d1553c98e35dbf3f51b6eac6b8036c1379d6d6bcdeb2702b7e195c82:922c64590222798bb761d5b6d8e72950