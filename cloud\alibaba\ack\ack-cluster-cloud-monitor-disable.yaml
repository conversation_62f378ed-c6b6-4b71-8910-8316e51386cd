id: ack-cluster-cloud-monitor-disable

info:
  name: Cloud Monitor for ACK Clusters - Disable
  author: ritikchaddha
  severity: medium
  description: |
    Ensure that Cloud Monitor is enabled for your Container Service for Kubernetes (ACK) clusters. Cloud Monitor relies on a specialized agent for accessing extra system resources and application services within virtual machine instances. The agent allows monitoring of metrics such as CPU utilization, specific disk traffic metrics, network traffic, and disk IO information. These metrics play a crucial role in observing signals and facilitating operational activities within your Kubernetes Engine clusters.
  reference:
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-ACK/enable-cloud-monitor.html
  metadata:
    max-request: 2
    verified: true
  tags: cloud,devops,aliyun,alibaba,aliyun-cloud-config,ack

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for (let cluster_id of iterate(template.cluster)) {
    set("cluster", cluster_id)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun cs GET /clusters --header "Content-Type=application/json;" --body "{}"

    extractors:
      - type: json
        name: cluster
        internal: true
        json:
          - '.[].cluster_id'

  - engine:
      - sh
      - bash
    source: |
      aliyun cs GET /clusters/$cluster/nodepools --header "Content-Type=application/json;" --body "{}"

    matchers:
      - type: word
        words:
          - 'kubernetes_config":'
          - 'cms_enabled": false'
        condition: and

    extractors:
      - type: dsl
        dsl:
          - 'cluster + " Cloud Monitor service is not enabled"'
# digest: 4a0a00473045022069baf44dc8c97e8026d54120e12b42a8b2b357a2dd490006b00d89291f18aebb022100e75b0f9abc1083ab553db314b51fba7ba3f51c66a2556c7f4a8d78b8270747dc:922c64590222798bb761d5b6d8e72950