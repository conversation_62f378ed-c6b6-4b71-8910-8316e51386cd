id: iam-user-password-change
info:
  name: Enable Self-Service Password Change for IAM Users
  author: princechaddha
  severity: high
  description: |
    Verifies that all Amazon IAM users have permissions to change their own console passwords, allowing access to 'iam:ChangePassword' for their accounts and 'iam:GetAccountPasswordPolicy' action.
  reference:
    - https://docs.aws.amazon.com/cli/latest/reference/iam/get-account-password-policy.html
  tags: cloud,devops,aws,amazon,iam,aws-cloud-config

self-contained: true
code:
  - engine:
      - sh
      - bash
    source: |
      aws iam get-account-password-policy --query 'PasswordPolicy.AllowUsersToChangePassword'

    matchers:
      - type: word
        words:
          - "false"

    extractors:
      - type: dsl
        dsl:
          - '"AllowUsersToChangePassword Policy is not enabled in your AWS account"'
# digest: 490a0046304402206bd744f6df0e569c9634f618174512b28065776339fff87a4f1d17356a433ffe022056631adaab285577d9c4c2c30749b91fbfb81d169bc1759bdd237fb845908bae:922c64590222798bb761d5b6d8e72950